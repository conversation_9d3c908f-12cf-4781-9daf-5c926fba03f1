<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;

class ListQuoteHeadings extends ListRecords
{
    protected static string $resource = QuoteHeadingResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Quote Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
