<?php

namespace App\Http\Controllers;

use App\Models\Shipment;
use App\Models\ShipmentEmail;
use Illuminate\Http\Request;

class ShipmentController extends Controller
{
    public function track(Request $request)
{

    $request->validate([
        'chassis_number' => 'required|string',
        'email' => 'required|email',
    ]);
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    $shipment = Shipment::where('chassis_number', $request->chassis_number)->first();

    if (!$shipment) {
        return redirect()->back()->with('error', 'No shipment found for the provided plate number.');
    }

    $existingEmail = ShipmentEmail::where('chassis_number', $request->chassis_number)
                                  ->where('email', $request->email)
                                  ->first();

    if (!$existingEmail) {

        ShipmentEmail::create([
            'chassis_number' => $request->chassis_number,
            'email' => $request->email,
                        ]);
                    }

    return view('shipment_detail', compact('shipment', 'subscriptionHeading'));
}
}
