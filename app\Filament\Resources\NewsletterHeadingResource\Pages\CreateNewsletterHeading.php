<?php

namespace App\Filament\Resources\NewsletterHeadingResource\Pages;

use App\Filament\Resources\NewsletterHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateNewsletterHeading extends CreateRecord
{

    public function getTitle():string
    {
        return 'Create Subscription Heading';
    }


    protected static string $resource = NewsletterHeadingResource::class;
}
