<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ServiceResource\Pages;
use App\Filament\Resources\ServiceResource\RelationManagers;
use App\Models\Service;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'clarity-tags-solid-alerted';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Name')
                    ->required(),
                Forms\Components\FileUpload::make('image')
                    ->label('Featured Image')
                    ->image(),
                Forms\Components\RichEditor::make('short_description')
                    ->label('Short Description')
                    ->required(),
                Forms\Components\TextInput::make('description_title')
                    ->label('Description Title')
                    ->required(),
                    Forms\Components\RichEditor::make('description')
                    ->label('Description')
                    ->required(),
                Forms\Components\RichEditor::make('service_offered')
                    ->label('Service Offered')
                    ->required(),
                Forms\Components\FileUpload::make('service_offered_image')
                    ->label('Service Offered Image')->required()
                    ->image(),
                Forms\Components\FileUpload::make('icon')
                    ->label('Icon')->required(),



            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()->label('Name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('short_description')
                    ->searchable()->label('Short Description')
                    ->sortable(),
                    Tables\Columns\TextColumn::make('description_title')
                    ->searchable()->label('Description Title')
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()->label('Description')
                    ->sortable(),
                Tables\Columns\ImageColumn::make('image')
                    ->searchable()->label('Featured Image')
                    ->sortable(),
                Tables\Columns\TextColumn::make('service_offered')
                    ->searchable()->label('Service Offered')
                    ->sortable(),
                Tables\Columns\ImageColumn::make('service_offered_image')
                    ->searchable()->label('Service Offered Image')
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable()->label('Slug')
                    ->sortable(),
                Tables\Columns\ImageColumn::make('icon')
                    ->searchable()->label('Icon')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServices::route('/'),
            'create' => Pages\CreateService::route('/create'),
            'edit' => Pages\EditService::route('/{record}/edit'),
        ];
    }
}
