<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\Blog;
use App\Filament\Resources\BlogResource\Pages;
use App\Filament\Resources\BlogResource\RelationManagers;
use App\Models\BlogPost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BlogResource extends Resource
{
    protected static ?string $model = BlogPost::class;

    protected static ?string $navigationIcon = 'heroicon-s-pencil-square';

    protected static ?string $cluster = Blog::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\FileUpload::make('featured_image')
                    ->label('Featured Image')
                    ->image(),
                Forms\Components\RichEditor::make('short_description')
                    ->label('Short Description')
                    ->required(),
                Forms\Components\RichEditor::make('description')
                    ->label('Description')
                    ->required(),
                Forms\Components\RichEditor::make('detail_description')
                    ->label('Detail Description')
                    ->required(),
                Forms\Components\FileUpload::make('detail_image1')
                    ->label('Detail Image 1')
                    ->image(),
                Forms\Components\FileUpload::make('detail_image2')
                    ->label('Detail Image 2')
                    ->image(),
                Forms\Components\RichEditor::make('quote')
                    ->label('Quote')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()->label('Title')
                    ->sortable(),
                Tables\Columns\TextColumn::make('short_description')
                    ->searchable()->label('Short Description')
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()->label('Description')
                    ->sortable(),
                Tables\Columns\TextColumn::make('detail_description')
                    ->searchable()->label('Detail Description')
                    ->sortable(),
                Tables\Columns\TextColumn::make('detail_image1')
                    ->searchable()->label('Detail Image 1')
                    ->sortable(),
                Tables\Columns\TextColumn::make('detail_image2')
                    ->searchable()->label('Detail Image 2')
                    ->sortable(),
                Tables\Columns\TextColumn::make('quote')
                    ->searchable()->label('Quote')
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable()->label('Slug')
                    ->sortable(),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlogs::route('/'),
            'create' => Pages\CreateBlog::route('/create'),
            'edit' => Pages\EditBlog::route('/{record}/edit'),
        ];
    }
}
