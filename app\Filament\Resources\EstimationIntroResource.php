<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\EstimationIntroResource\Pages;
use App\Filament\Resources\EstimationIntroResource\RelationManagers;
use App\Models\EstimationIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EstimationIntroResource extends Resource
{
    protected static ?string $model = EstimationIntro::class;

    protected static ?string $navigationIcon = 'heroicon-s-pencil-square';
    protected static ?string $cluster = HomePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'Estimation Heading';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->label('Description')
                    ->required(),
                Forms\Components\FileUpload::make('bg_image')
                    ->label('Background Image')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('bg_image')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEstimationIntros::route('/'),
            'create' => Pages\CreateEstimationIntro::route('/create'),
            'edit' => Pages\EditEstimationIntro::route('/{record}/edit'),
        ];
    }
}
