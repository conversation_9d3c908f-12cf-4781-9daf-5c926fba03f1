<?php

namespace App\Filament\Resources\BlogIntroResource\Pages;

use App\Filament\Resources\BlogIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateBlogIntro extends CreateRecord
{
    public function getTitle(): string
    {
        return 'Create Blog Heading';
    }
    protected static string $resource = BlogIntroResource::class;
}
