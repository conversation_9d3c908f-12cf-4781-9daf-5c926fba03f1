<?php

namespace App\Http\Controllers;

use App\Filament\Clusters\Blog;
use App\Filament\Clusters\Gallery;
use App\Models\AboutCompany;
use App\Models\Approach;
use App\Models\BlogIntro;
use App\Models\BlogPost;
use App\Models\Contact;
use App\Models\CostEstimationHeading;
use App\Models\EstimationIntro;
use App\Models\GalleryHeading;
use App\Models\GalleryImage;
use App\Models\MissionVission;
use App\Models\News;
use App\Models\NewsletterHeading;
use App\Models\Overview;
use App\Models\Project;
use App\Models\QuoteHeading;
use App\Models\RequestQuote;
use App\Models\Service;
use App\Models\ServiceHeading;
use App\Models\ServiceIntro;
use App\Models\ServiceSolution;
use App\Models\SiteIdentity;
use App\Models\Slider;
use App\Models\Solution;
use App\Models\TestimonialsContent;
use App\Models\TestimonialsProfile;
use App\Models\WhatWeDo;
use App\Models\WhatWeDoList;
use App\Models\WhyChooseUs;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        $validated = $request->validate([
            'q' => 'required|string|min:3|max:255'
        ]);

        $query = $validated['q'];

        $whatwedo = WhatWeDo::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if ($whatwedo) {
                return redirect()->route('home')->withFragment('whatwedo')->with('success', 'Search results found.');
            }
        $whatwedolist = WhatWeDoList::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($whatwedolist){
                return redirect()->route('home')->withFragment('whatwedolist')->with('success', 'Search results found.');
            }
        $whychooseus = WhyChooseUs::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($whychooseus){
                return redirect()->route('home')->withFragment('whychooseus')->with('success', 'Search results found.');
            }
        $requestquoteheading = RequestQuote::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($requestquoteheading){
                return redirect()->route('home')->withFragment('requestquoteheading')->with('success', 'Search results found.');
            }
        $aboutcompany = AboutCompany::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('counter', 'LIKE', "%{$query}%");
        })
        ->first();
            if($aboutcompany){
                return redirect()->route('home')->withFragment('aboutcompany')->with('success', 'Search results found.');
            }
        $estimation = EstimationIntro::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($estimation){
                return redirect()->route('home')->withFragment('estimation')->with('success', 'Search results found.');
            }
        $solutions = Solution::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($solutions){
                return redirect()->route('home')->withFragment('solutions')->with('success', 'Search results found.');
            }
        $testimonialcontent = TestimonialsContent::where(function($q) use ($query) {
            $q->where('message', 'LIKE', "%{$query}%")
              ->orWhere('name', 'LIKE', "%{$query}%")->orWhere('position', 'LIKE', "%{$query}%");
        })
        ->first();
            if($testimonialcontent){
                return redirect()->route('home')->withFragment('testimonialcontent')->with('success', 'Search results found.');
            }
        $serviceintro = ServiceIntro::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($serviceintro){
                return redirect()->route('home')->withFragment('serviceintro')->with('success', 'Search results found.');
            }
        $service = Service::where(function($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")
              ->orWhere('description', 'LIKE', "%{$query}%")->orWhere('short_description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($service){
                return redirect()->route('home')->withFragment('service')->with('success', 'Search results found.');
            }
        $blogintro = BlogIntro::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($blogintro){
                return redirect()->route('home')->withFragment('blogintro')->with('success', 'Search results found.');
            }
        $blog = BlogPost::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('short_description', 'LIKE', "%{$query}%")->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($blog){
                return redirect()->route('home')->withFragment('blog')->with('success', 'Search results found.');
            }
        $projects = Project::where(function($q) use ($query) {
            $q->where('name', 'LIKE', "%{$query}%")->orWhere('tag', 'LIKE', "%{$query}%")
              ->orWhere('short_description', 'LIKE', "%{$query}%")->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($projects){
                return redirect()->route('home')->withFragment('projects')->with('success', 'Search results found.');
            }
        $approaches = Approach::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('counter', 'LIKE', "%{$query}%");
        })
        ->first();
            if($approaches){
                return redirect()->route('home')->withFragment('approaches')->with('success', 'Search results found.');
            }
        $siteId = SiteIdentity::where(function($q) use ($query) {
            $q->where('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($siteId){
                return redirect()->route('home')->withFragment('siteId')->with('success', 'Search results found.');
            }
        $overview = Overview::where(function($q) use ($query) {
            $q->where('overview', 'LIKE', "%{$query}%");
        })
        ->first();
            if($overview){
                return redirect()->route('overview')->withFragment('overview')->with('success', 'Search results found.');
            }
        $missionvission = MissionVission::where(function($q) use ($query) {
            $q->where('mission_vission', 'LIKE', "%{$query}%");
        })
        ->first();
            if($missionvission){
                return redirect()->route('mission')->withFragment('missionvission')->with('success', 'Search results found.');
            }
        $serviceheading = ServiceHeading::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($serviceheading){
                return redirect()->route('services')->withFragment('serviceheading')->with('success', 'Search results found.');
            }
        $slider = Slider::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($slider){
                return redirect()->route('home')->withFragment('slider')->with('success', 'Search results found.');
            }
        $costheading = CostEstimationHeading::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($costheading){
                return redirect()->route('services')->withFragment('costheading')->with('success', 'Search results found.');
            }
        $servicesolution = ServiceSolution::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%")->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($servicesolution){
                return redirect()->route('services')->withFragment('servicesolution')->with('success', 'Search results found.');
            }
        $quoteheading = QuoteHeading::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($quoteheading){
                return redirect()->route('services')->withFragment('quoteheading')->with('success', 'Search results found.');
            }
        $galleryheading = GalleryHeading::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('content', 'LIKE', "%{$query}%");
        })
        ->first();
            if($galleryheading){
                return redirect()->route('home')->withFragment('galleryheading')->with('success', 'Search results found.');
            }
        $galleryimages = GalleryImage::where(function($q) use ($query) {
            $q->where('caption', 'LIKE', "%{$query}%");
        })
        ->first();
            if($galleryimages){
                return redirect()->route('home')->withFragment('galleryimages')->with('success', 'Search results found.');
            }
        $news = News::where(function($q) use ($query) {
            $q->where('title', 'LIKE', "%{$query}%")
              ->orWhere('short_description', 'LIKE', "%{$query}%")->orWhere('description', 'LIKE', "%{$query}%");
        })
        ->first();
            if($news){
                return redirect()->route('news')->withFragment('news')->with('success', 'Search results found.');
            }
        $contact = Contact::where(function($q) use ($query) {
            $q->where('address', 'LIKE', "%{$query}%")
              ->orWhere('phone', 'LIKE', "%{$query}%")->orWhere('email', 'LIKE', "%{$query}%");
        })
        ->first();
            if($contact){
                return redirect()->route('contact')->withFragment('contact')->with('success', 'Search results found.');
            }
        $subscriptionHeading = NewsletterHeading::where(function($q) use ($query) {
            $q->where('heading', 'LIKE', "%{$query}%");
        })
        ->first();
            if($subscriptionHeading){
                return redirect()->route('home')->withFragment('subscriptionHeading')->with('success', 'Search results found.');
            }

        return redirect()->route('home')->with('error', 'No results found.');
    }
}
