@extends('frontend.layouts.master')
@section('content')
    <!-- Swiper -->
    @include('frontend.layouts.swiper')
    <!-- Swiper -->


    <!-- WHAT WE DO SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-gray tw-what-wedo-area" id="whatwedo">
        <div class="container">
            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif
            <!-- TITLE START -->
            <div class="section-head center wt-small-separator-outer">
                <div class="wt-small-separator site-text-primary">
                    <div>What We Do</div>
                </div>
                @if(!empty($whatwedo->title))
                <h2 class="wt-title">{{$whatwedo->title}}</h2>
                <p class="section-head-text">
                    {{$whatwedo->content}}
                </p>
                @endif
            </div>
            <!-- TITLE END -->

            <div class="tw-what-wedo-section">

                <div class="row">
                    <div class="col-xl-5 col-lg-5 col-md-12">
                        <div class="tw-what-wedo-media">
                            @if(!empty($whatwedo->featured_image))
                            <img src="{{ asset('storage/' . $whatwedo->featured_image) }}" alt="Logistics Services">
                            @endif
                        </div>
                    </div>

                    <div class="col-xl-7 col-lg-7 col-md-12" id="whatwedolist">
                        <div class="tw-service-icon-box-wrap">
                            <!-- ROAD FREIGHT -->
                            @foreach($services->take(3) as $i=> $service)
                            @if(!empty($service->name))
                            <div class="service-icon-box-two">

                                <div class="service-icon-box-two-media">
                                    <img src="{{ asset('storage/'. $service->icon) }}" alt="Road Freight">
                                </div>

                                <div class="service-icon-box-title">
                                    <h3 class="wt-title">
                                        <a href="{{route('service-single', $service->slug)}}">
                                            <span class="site-text-primary">0{{$i+=1}}.</span> {{$service->name}}
                                        </a>
                                    </h3>
                                    <p>
                                        {!! $service->short_description !!}
                                    </p>
                                </div>

                            </div>
                            @endif
                            @endforeach

                            {{-- <!-- SHIPPING FREIGHT -->
                            <div class="service-icon-box-two">

                                <div class="service-icon-box-two-media">
                                    <img src="frontend/images/icons/pic2.png" alt="Shipping Freight">
                                </div>

                                <div class="service-icon-box-title">
                                    <h3 class="wt-title">
                                        <a href="services-detail.html">
                                            <span class="site-text-primary">02.</span> Shipping Freight
                                        </a>
                                    </h3>
                                    <p>
                                        Comprehensive ocean freight services, offering cost-effective solutions to ensure
                                        your cargo reaches global markets safely and efficiently.
                                    </p>
                                </div>

                            </div>

                            <!-- AIR FREIGHT -->
                            <div class="service-icon-box-two">

                                <div class="service-icon-box-two-media">
                                    <img src="frontend/images/icons/pic3.png" alt="Air Freight">
                                </div>

                                <div class="service-icon-box-title">
                                    <h3 class="wt-title">
                                        <a href="services-detail.html">
                                            <span class="site-text-primary">03.</span> Air Freight
                                        </a>
                                    </h3>
                                    <p>
                                        Fast, secure, and reliable air freight solutions tailored for urgent, high-value, or
                                        time-sensitive cargo across the globe.
                                    </p>
                                </div>

                            </div> --}}
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
    <!-- WHAT WE DO SECTION END -->

    <!-- WHY CHOOSE US SECTION START -->
    <div class="section-full-wrap site-bg-gray tw-why-choose-area" id="whychooseus">
        <div class="section-full p-t120 p-b120 tw-why-choose-area-top bg-cover bg-no-repeat"
            @if(!empty($whychooseus->bg_image)) style="background-image: url({{ asset('storage/' . $whychooseus->bg_image) }});" @endif>
            <div class="container">

                <div class="tw-why-choose-section">

                    <div class="row">
                        <div class="col-xl-5 col-lg-5 col-md-12">
                            <div class="tw-why-choose-left">
                                <!-- TITLE START-->
                                <div class="section-head left wt-small-separator-outer">
                                    <div class="wt-small-separator site-text-primary">
                                        <div>Why Choose Us</div>
                                    </div>
                                    @if(!empty($whychooseus->title))
                                    <h2 class="wt-title">{{$whychooseus->title}}</h2>
                                    @endif
                                </div>
                                <!-- TITLE END-->
                                @if(!empty($whychooseus->content))
                                {!! $whychooseus->content !!}
                                @endif
                                {{-- <strong>
                                    Committed to excellence, we deliver tailored logistics solutions that ensure reliability, efficiency, and satisfaction.
                                </strong>
                                <p class="section-head-text">
                                    We combine industry expertise, advanced technology, and personalized customer service to streamline your logistics needs. Our dedicated team ensures smooth operations, offering comprehensive multimodal transportation solutions including road, sea, and air freight services. We prioritize your cargo's safety, timely delivery, and seamless handling, making your logistics process effortless and reliable.
                                </p> --}}
                                <div class="tw-why-choose-left-bottom">
                                    <a href="/about-us" class="btn-half site-button"><span>Learn More</span><em></em></a>
                                    <div class="sign-font">
                                        <span>GulfIngot</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-7 col-lg-7 col-md-12">
                            <div class="tw-why-choose-right">
                                <div class="tw-why-choose-media1 shine-effect">
                                    <div class="shine-box">
                                        @if(!empty($whychooseus->featured_img1))
                                        <img src="{{asset('storage/' .$whychooseus->featured_img1)}}" alt="Reliable Logistics">
                                        @endif
                                    </div>
                                </div>
                                <div class="tw-why-choose-media2 slide-top shine-effect">
                                    <div class="shine-box">
                                        @if(!empty($whychooseus->featured_img2))
                                        <img src="{{asset('storage/' .$whychooseus->featured_img2)}}" alt="Efficient Cargo Handling">
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tw-hilite-text-wrap2">
                        <div class="tw-hilite-text">
                            <span>Logistics</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tw-any-help-section">
            <div class="container">
                <div class="tw-any-help-inner">
                    <img src="frontend/images/w-choose/qt-icon.png" alt="Support Icon">
                    <span class="tw-24">24/7 Active Support Team</span>
                    <span class="tw-help-title">Need Immediate Support Or Assistance?</span>
                    <span class="tw-help-number">(+251 967333388)</span>
                </div>
            </div>
        </div>

    </div>
    <!-- WHY CHOOSE US SECTION END -->

    <!-- BOOKING SECTION START -->

    <div class="section-full p-t120 p-b90 site-bg-gray tw-booking-area"
    @if(!empty( $requestquoteheading->bg_image)) style="background-image: url({{asset('storage/'. $requestquoteheading->bg_image)}});" @endif>

        <div class="container" id="tracking">
            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer" id="requestquoteheading">
                <div class="wt-small-separator site-text-primary">
                    <div>Request A Quote</div>
                </div>
                @if(!empty($requestquoteheading->title))
                <h2 class="wt-title">{{$requestquoteheading->title}}</h2>
                <p class="section-head-text">
                    {{$requestquoteheading->content}}
                </p>
                @endif
            </div>
            <!-- TITLE END-->
        </div>

        <div class="container">
            <div class="tw-booking-section">
                <div class="row">


                    <div class="col-xl-3 col-lg-3 col-md-12">
                        <div class="tw-booking-media">
                            <div class="media">
                                @if(!empty( $requestquoteheading->featured_image))
                                <img src="{{asset('storage/'. $requestquoteheading->featured_image)}}" alt="#">
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-9 col-lg-9 col-md-12">
                        <div class="tw-booking-form">

                            <div class="row booking-tab-container">
                                <div class="col-lg-2 col-md-2 booking-tab-menu">
                                    <div class="list-group">
                                        <a href="#" class="list-group-item active text-center">
                                            <div class="media">
                                                <img src="frontend/images/booking/icon1.png" alt="">
                                            </div>
                                            <span>Request A Quote</span>
                                        </a>
                                        <a href="#" class="list-group-item text-center">
                                            <div class="media">
                                                <img src="frontend/images/booking/icon2.png" alt="">
                                            </div>
                                            <span>Track & Trace</span>
                                        </a>

                                    </div>
                                </div>
                                <div class="col-lg-10 col-md-10 booking-tab">
                                    <!-- flight section -->
                                    <div class="booking-tab-content active">
                                        <form action="{{ route('quotes.store') }}" method="POST">
                                            @csrf
                                            <div class="row">
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="company_name" type="text" required
                                                            class="form-control" placeholder="Company Name">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="username" type="text" required
                                                            class="form-control" placeholder="Full Name">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="email" type="email" required
                                                            class="form-control" placeholder="Email">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="phone" type="text" required
                                                            class="form-control" placeholder="Phone">
                                                    </div>
                                                </div>


                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <select id="Freight_Type" name="freight_type" class="form-select">
                                                            <option selected>Service Type</option>
                                                            <option>Import</option>
                                                            <option>Export</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <input name="city_of_departure" type="text" required
                                                            class="form-control" placeholder="Origin">
                                                    </div>
                                                </div>

                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <input name="delivery_city" type="text" required
                                                            class="form-control" placeholder="Destination">
                                                    </div>
                                                </div>

                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <select id="Incoterms" name="incoterms" class="form-select">
                                                            <option selected>Commodity Type </option>
                                                                <option>Dangerous</option>
                                                                <option>Non Dangerous</option>
                                                        </select>
                                                    </div>
                                                </div>


                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="weight" type="number" required
                                                            class="form-control" placeholder="Weight">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="height" type="number" required
                                                            class="form-control" placeholder="Height">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="width" type="number" required
                                                            class="form-control" placeholder="Width">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="length" type="number" required
                                                            class="form-control" placeholder="Length">
                                                    </div>
                                                </div>
                                                <div class="col-lg-12">
                                                    <div class="mb-3">
                                                        <textarea name="message" type="text" required
                                                            class="form-control" placeholder="Description"></textarea>
                                                    </div>
                                                </div>

                                                <div class="col-lg-12">
                                                    <div class="tw-inline-checked mt-2 mb-3">
                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="fragile" value="0">
                                                            <input type="checkbox" class="form-check-input" name="fragile" value="1"
                                                                id="exampleCheck1">
                                                            <label class="form-check-label"
                                                                for="exampleCheck1">Fragile</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="express_delivery" value="0">
                                                            <input type="checkbox" class="form-check-input" name="express_delivery" value="1"
                                                                id="exampleCheck2">
                                                            <label class="form-check-label" for="exampleCheck2">Express
                                                                Delivery</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="insurance" value="0">
                                                            <input type="checkbox" class="form-check-input" name="insurance" value="1"
                                                                id="exampleCheck3">
                                                            <label class="form-check-label"
                                                                for="exampleCheck3">Insurance</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="packaging" value="0">
                                                            <input type="checkbox" class="form-check-input" name="packaging" value="1"
                                                                id="exampleCheck4">
                                                            <label class="form-check-label"
                                                                for="exampleCheck4">Packaging</label>
                                                        </div>
                                                    </div>
                                                </div>



                                                <div class="col-lg-12 col-md-12">
                                                    <div class="tw-booking-footer">
                                                        <div class="tw-booking-footer-btn">
                                                            <button type="submit" class="btn-half site-button">
                                                                <span>Submit Now</span><em></em>
                                                            </button>
                                                        </div>
                                                        <span class="tw-booking-footer-text">Quote</span>
                                                    </div>

                                                </div>

                                            </div>

                                        </form>
                                    </div>
                                    <!-- train section -->
                                    <div class="booking-tab-content" id="tracking">
                                        {{-- <div style="display: flex; margin: 20px 0; gap: 10px;">
                                            <button type="submit" class="btn-half site-button active" data-tab="1">
                                                <span>Import</span><em></em>
                                            </button>
                                            <button type="submit" class="btn-half site-button" data-tab="2">
                                                <span>Export</span><em></em>
                                            </button>
                                        </div> --}}
                                    <div class="tab-content active" data-tab="1">
                                        <form id="trackForm" class="track-and-trace-form" method="GET" action="{{ route('track.shipment') }}">
                                            @csrf
                                            <div class="row">

                                                 <div class="row">
                                                    <div class="col-lg-12 col-md-12">
                                                        <div class="mb-3">
                                                            <select id="shipment_type" class="form-select" name="shipment_type" required>
                                                                <option value="" selected>Select Shipment Type</option>
                                                                <option value="ocean">Ocean Cargo</option>
                                                                <option value="air">Air Cargo</option>
                                                                <option value="lcl">LCL Cargo</option>
                                                                <option value="parcel">Parcel</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                @if(session('error'))
                                                    <div class="alert alert-danger">
                                                        {{ session('error') }}
                                                    </div>
                                                @endif

                                                <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <input type="email" name="email" class="form-control" id="email" rows="3" placeholder="Enter your email" required>
                                                    </div>
                                                </div>

                                                {{-- <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <input type="text" name="chassis_number" class="form-control" id="chassis_number" rows="3" placeholder="Enter Plate Number" required></input>
                                                    </div>
                                                </div> --}}


                                                {{-- <div class="col-lg-12">
                                                    <div class="tw-inline-checked mt-2 mb-3">
                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                id="Fragile1">
                                                            <label class="form-check-label" for="Fragile1">Fragile</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                id="Express2">
                                                            <label class="form-check-label" for="Express2">Express
                                                                Delivery</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                id="Insurance3">
                                                            <label class="form-check-label"
                                                                for="Insurance3">Insurance</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                id="packaging4">
                                                            <label class="form-check-label"
                                                                for="packaging4">Packaging</label>
                                                        </div>
                                                    </div>
                                                </div> --}}


                                                <!-- Dynamic Tracking Type Selector -->
                                                <div class="col-lg-12 col-md-12" id="trackingTypeContainer" style="display: none;">
                                                    <div class="mb-3">
                                                        <select id="tracking_type" class="form-select" name="tracking_type">
                                                            <!-- Options will be populated by JavaScript -->
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Dynamic Tracking Number Input -->
                                                <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <input type="text"
                                                               name="tracking_number"
                                                               class="form-control"
                                                               id="tracking_number"
                                                               placeholder="Select shipment type first"
                                                               required
                                                               data-placeholders='{
                                                                   "ocean_bl": "Enter Bill of Lading Number (e.g. BL12345678)",
                                                                   "ocean_container": "Enter Container Number (e.g. CAXU1234567)",
                                                                   "air": "Enter Airway Bill Number (e.g. 123-45678901)",
                                                                   "lcl_bl": "Enter Bill of Lading Number",
                                                                   "lcl_booking": "Enter Booking Reference",
                                                                   "parcel": "Enter Parcel Tracking Number"
                                                               }'>
                                                    </div>
                                                </div>

                                                <div class="col-lg-12 col-md-12">
                                                    <div class="tw-booking-footer">
                                                        <div class="tw-booking-footer-btn">
                                                            <button type="submit" class="btn-half site-button">
                                                                <span>Track & Trace</span><em></em>
                                                            </button>
                                                        </div>
                                                        <span class="tw-booking-footer-text">Trace</span>
                                                    </div>

                                                </div>

                                            </div>

                                        </form>
                                    </div>
                                        {{-- <div class="tab-content" data-tab="2">Content for Tab 2</div> --}}
                                    </div>


                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>

    <!-- BOOKING SECTION END -->

    <div class="section-full site-bg-dark tw-we-achived">

        <div class="container">

            <div class="tw-we-achived-section" id="aboutcompany">

                <!-- 1 -->
                @foreach($aboutcompany->take(3) as $list)
                @if(!empty($list->title))
                <div class="tw-we-achived-box-warp {{ $loop->iteration == 2 ? 'bg-skew' : '' }}">
                    <div class="tw-we-achived-box">
                        <h2 class="counter">{{$list->counter}}</h2>
                        <span>{{$list->title}}</span>
                    </div>
                </div>
                @endif
                @endforeach

                {{-- <!-- 2 -->
                <div class="tw-we-achived-box-warp bg-skew">
                    <div class="tw-we-achived-box ">
                        <h2 class="counter">15</h2>
                        <span>Year of experience </span>
                    </div>
                </div>

                <!-- 3 -->
                <div class="tw-we-achived-box-warp">
                    <div class="tw-we-achived-box">
                        <h2 class="counter">60</h2>
                        <span>Kg Minimum lot valume</span>
                    </div>
                </div> --}}

            </div>
        </div>

    </div>

    <!-- Estimation SECTION START -->
    <div class="section-full p-t120 site-bg-white tw-estimation-area">

        <div class="container">

            <div class="wt-separator-two-part">
                <div class="row wt-separator-two-part-row">
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-left">
                        <!-- TITLE START-->
                        <div class="section-head left wt-small-separator-outer" id="estimation">
                            <div class="wt-small-separator site-text-primary">
                                <div>Estimation</div>
                            </div>
                            @if(!empty($estimation->title))
                            <h2 class="wt-title">{{$estimation->title}}</h2>
                            <p class="section-head-text">
                                {{$estimation->content}}
                            </p>
                            @endif
                        </div>
                        <!-- TITLE END-->
                    </div>
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-right text-right">
                        <a href="/about-us" class="btn-half site-button"><span>Learn More</span><em></em></a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tw-estimation-section bg-cover bg-no-repeat"
            style="background-image: url(frontend/images/background/bg-2.jpg);">
            <div class="container" id="solutions">
                <div class="row">
                    <!-- Solution 1 -->
                    @foreach($solutions as $i=> $solution)
                    @if(!empty($solution->title))
                    <div class="col-xl-4 col-lg-4 col-md-6">
                        <div class="tw-est-section-block">
                            <div class="tw-est-section-block-content">
                                <span class="tw-est-section-number">0{{$i+=1}}</span>
                                <h3 class="tw-title">{{$solution->title}}</h3>
                                <p>
                                    {{$solution->title}}
                                </p>
                                <a href="/services" class="site-button-2-outline"><i
                                        class="fa fa-angle-right"></i></a>
                            </div>
                        </div>
                    </div>
                    @endif
                    @endforeach
                    {{-- <!-- Solution 2 -->
                    <div class="col-xl-4 col-lg-4 col-md-6">
                        <div class="tw-est-section-block">
                            <div class="tw-est-section-block-content">
                                <span class="tw-est-section-number">02</span>
                                <h3 class="tw-title">Extensive Warehousing Network</h3>
                                <p>
                                    With strategically located warehouses, we provide flexible drop-off and pickup points, ensuring smooth logistics operations and hassle-free transportation.
                                </p>
                                <a href="about-1.html" class="site-button-2-outline"><i
                                        class="fa fa-angle-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <!-- Solution 3 -->
                    <div class="col-xl-4 col-lg-4 col-md-12">
                        <div class="tw-est-section-block">
                            <div class="tw-est-section-block-content">
                                <span class="tw-est-section-number">03</span>
                                <h3 class="tw-title">Seamless Shipment Tracking</h3>
                                <p>
                                    Stay updated with real-time tracking, providing you with complete visibility of your shipment's journey, ensuring security and peace of mind.
                                </p>
                                <a href="about-1.html" class="site-button-2-outline"><i
                                        class="fa fa-angle-right"></i></a>
                            </div>
                        </div>
                    </div> --}}

                </div>
            </div>

        </div>

    </div>
    <!-- Estimation SECTION END -->

    <!-- TESTIMONIALS SECTION START -->
    <div class="section-full bg-cover p-t120 p-b120 bg-cover tw-testimonial-3-area" style="background-image:url(images/background/bg-6.jpg);">
        <!-- TITLE START-->
        <div class="section-head center wt-small-separator-outer">
            <div class="wt-small-separator site-text-primary">
                <div> Respected</div>



            </div>
            <h2 class="wt-title"> Clients & partners</h2>
        </div>
        <!-- TITLE END-->
        <div class="tw-testimonial-3-area-inner">
            <div class="container-fluid">

                <div class="row">

                    <div class="col-xl-12 col-lg-12 col-md-12">

                        <div class="slider-testimonial-3-wrap">
                            <!-- THUMBNAILS -->
                            <div class="slick-testimonials-3-thumb">
                                @foreach($testimonialprofile as $profile)
                                @if(!empty($profile->profile))
                                <div class="slick-item">
                                    <div class="slick-testimonials-3-thumbpic">
                                        <img src="{{asset('storage/'. $profile->profile)}}" alt="one">
                                    </div>
                                </div>
                                @endif
                                @endforeach
                                {{-- <div class="slick-item">
                                    <div class="slick-testimonials-3-thumbpic">
                                        <img src="frontend/images/testimonials/pic2.jpg" alt="two">
                                    </div>
                                </div>
                                <div class="slick-item">
                                    <div class="slick-testimonials-3-thumbpic">
                                        <img src="frontend/images/testimonials/pic3.jpg" alt="three">
                                    </div>
                                </div>
                                <div class="slick-item">
                                    <div class="slick-testimonials-3-thumbpic">
                                        <img src="frontend/images/testimonials/pic4.jpg" alt="four">
                                    </div>
                                </div> --}}
                            </div>
                            <!-- MAIN SLIDES -->
                            <div class="slick-testimonials-3" id="testimonialcontent">
                                @foreach($testimonialcontent as $content)
                                @if(!empty($content->message))
                                <div class="slick-item">
                                    <div class="tw-testimonials3-section">
                                        <div class="tw-testimonials3-text">
                                            <span class="tw-testimonials3-quote"><img src="frontend/images/quote.png" alt=""></span>
                                            {{$content->message}}
                                        </div>
                                        <div class="tw-testimonials3-name">{{$content->name}}</div>
                                        <div class="tw-testimonials3-postion">{{$content->position}}</div>
                                    </div>
                                </div>
                                @endif
                                @endforeach
                                {{-- <div class="slick-item">
                                    <div class="tw-testimonials3-section">
                                        <div class="tw-testimonials3-text">
                                            <span class="tw-testimonials3-quote"><img src="frontend/images/quote.png" alt=""></span>
                                            "Reliable, timely, and cost-effective solutions. Working with them has streamlined our entire logistics workflow."
                                        </div>
                                        <div class="tw-testimonials3-name">Giselle</div>
                                        <div class="tw-testimonials3-postion">Developer</div>
                                    </div>
                                </div>
                                <div class="slick-item">
                                    <div class="tw-testimonials3-section">
                                        <div class="tw-testimonials3-text">
                                            <span class="tw-testimonials3-quote"><img src="frontend/images/quote.png" alt=""></span>
                                            "Our international shipments have never been easier to manage. This company provides top-notch freight services!"
                                        </div>
                                        <div class="tw-testimonials3-name">Josephine</div>
                                        <div class="tw-testimonials3-postion">Web Designer</div>
                                    </div>
                                </div>
                                <div class="slick-item">
                                    <div class="tw-testimonials3-section">
                                        <div class="tw-testimonials3-text">
                                            <span class="tw-testimonials3-quote"><img src="frontend/images/quote.png" alt=""></span>
                                            "Their tracking system ensures complete transparency in shipments, making logistics hassle-free for our business."
                                        </div>
                                        <div class="tw-testimonials3-name">Penelope</div>
                                        <div class="tw-testimonials3-postion">Developer</div>
                                    </div>
                                </div> --}}
                            </div>

                        </div>

                    </div>

                </div>

            </div>
            <div class="tw-outer-border"></div>
        </div>

        <!-- Logo -->
        <div class="home-client3-outer">
            <div class="container">
                <div class="section-content">

                    <div class="owl-carousel home-client-carousel3 owl-btn-vertical-center">
                        @foreach($partners as $partner)
                        @if(!empty($partner->logo))
                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                            <a href="about-1.html"><img src="{{ asset('storage/' .$partner->logo) }}" alt=""></a></div>
                            </div>
                        </div>
                        @endif
                        @endforeach

                        {{-- <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w2.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w3.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w4.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w5.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w1.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w2.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w3.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w4.png" alt=""></a></div>
                            </div>
                        </div>

                        <div class="item">
                            <div class="ow-client-logo">
                                <div class="client-logo client-logo-media">
                                <a href="about-1.html"><img src="frontend/images/client-logo/w5.png" alt=""></a></div>
                            </div>
                        </div> --}}

                    </div>

                </div>
            </div>
        </div>
        <!-- Logo -->
    </div>
    <!-- TESTIMONIALS  SECTION End -->

    <!-- SERVICES SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-gray tw-service-gallery-style1-area tyre-mark-bg">

        <div class="services-gallery-block-outer2">
            <div class="container">

                <!-- TITLE START-->
                <div class="section-head center wt-small-separator-outer" id="serviceintro">
                    <div class="wt-small-separator site-text-primary">
                        <div>All services</div>
                    </div>
                    @if(!empty($serviceintro->title))
                    <h2 class="wt-title">{{$serviceintro->title}}</h2>
                    <p class="section-head-text">{{$serviceintro->description}}</p>
                    @endif
                </div>
                <!-- TITLE END-->

                <div class="section-content" id="service">
                    <div class="services-gallery-style1">
                        <div class="owl-carousel services-gallery-one owl-btn-bottom-center ">

                            <!-- COLUMNS 1 -->
                            @foreach($services as $i=> $service)
                            @if(!empty($service->name))
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a href="{{ route('service-single', $service->slug) }}">{{$service->name}}</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">0{{$i+=1}}</span>
                                                <p>{!! $service->short_description  !!}</p>
                                                <a href="{{ route('service-single', $service->slug) }}" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="{{asset('storage/'. $service->image)}}" alt="">
                                    </div>
                                </div>
                            </div>
                            @endif
                            @endforeach

                            {{-- <!-- COLUMNS 2 -->
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a href="services-detail.html">Road
                                                        Freight</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">02</span>
                                                <p>Our aim is to optimize and improve your supply chain so that we can give
                                                    you the best service.</p>
                                                <a href="services-detail.html" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="frontend/images/s-gallery/2.jpg" alt="">
                                    </div>
                                </div>
                            </div>

                            <!-- COLUMNS 3 -->
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a href="services-detail.html">Ocean
                                                        Freight</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">03</span>
                                                <p>Our aim is to optimize and improve your supply chain so that we can give
                                                    you the best service.</p>
                                                <a href="services-detail.html" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="frontend/images/s-gallery/3.jpg" alt="">
                                    </div>
                                </div>
                            </div>

                            <!-- COLUMNS 4 -->
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a href="services-detail.html">Rail
                                                        Freight</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">04</span>
                                                <p>Our aim is to optimize and improve your supply chain so that we can give
                                                    you the best service.</p>
                                                <a href="services-detail.html" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="frontend/images/s-gallery/4.png" alt="">
                                    </div>
                                </div>
                            </div>

                            <!-- COLUMNS 5 -->
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a
                                                        href="services-detail.html">Warehousing</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">05</span>
                                                <p>Our aim is to optimize and improve your supply chain so that we can give
                                                    you the best service.</p>
                                                <a href="services-detail.html" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="frontend/images/s-gallery/5.png" alt="">
                                    </div>
                                </div>
                            </div>

                            <!-- COLUMNS 6 -->
                            <div class="item ">
                                <div class="service-box-style1">
                                    <div class="service-content">
                                        <div class="service-content-inner">
                                            <div class="service-content-top">
                                                <h3 class="service-title-large"><a href="services-detail.html">Project
                                                        Cargo</a></h3>
                                            </div>
                                            <div class="service-content-bottom">
                                                <span class="service-title-large-number">06</span>
                                                <p>Our aim is to optimize and improve your supply chain so that we can give
                                                    you the best service.</p>
                                                <a href="services-detail.html" class="site-button-2">View Detail</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-media">
                                        <img src="frontend/images/s-gallery/6.jpg" alt="">
                                    </div>
                                </div>
                            </div> --}}


                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tw-hilite-text-wrap">
            <div class="tw-hilite-text right">
                <span>Services</span>
            </div>
        </div>

    </div>
    <!-- SERVICES SECTION END -->

    <!-- COMPANY APPROCH SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-white tw-company-approch-area">

        <div class="container">

            <div class="wt-separator-two-part">
                <div class="row wt-separator-two-part-row">
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-left">
                        <!-- TITLE START-->
                        <div class="section-head left wt-small-separator-outer">
                            <div class="wt-small-separator site-text-primary">
                                <div>Company Approch</div>
                            </div>
                            <h2 class="wt-title">Reliable Logistic & Transport Solutions</h2>
                        </div>
                        <!-- TITLE END-->
                    </div>
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-right text-right">
                        <a href="about-1.html" class="btn-half site-button"><span>Read More</span><em></em></a>
                    </div>
                </div>
            </div>


            <div class="tw-company-approch-section" id="approaches">
                <div class="tw-company-approch-inner">
                    @foreach($approaches ->take(1) as $approach)
                    @if(!empty($approach->title))
                    <div class="tw-company-years">
                        <div class="light">
                            <h1>{{$approach->counter}}</h1>
                            <img src="{{asset('storage/'.$approach->icon)}}" alt="">
                        </div>
                        <div class="tw-company-info">
                            <span>{{$approach->title}}</span>
                        </div>
                    </div>
                    @endif
                    @endforeach

                    <div class="row">

                        <!--block 1-->
                        @foreach($approaches ->skip(1) as $approach)
                        @if(!empty($approach->title))
                        <div class="col-lg-4 col-md-12">
                            <div class="counter-outer-two">
                                <div class="tw-counter-media">
                                    <img src="{{asset('storage/'.$approach->icon)}}" alt="">
                                </div>
                                <div class="icon-content">
                                    <div class="tw-count-number site-text-primary"><span
                                            class="counter site-text-primary">{{$approach->counter}}</span>+</div>
                                    <h3 class="icon-content-info">{{$approach->title}}</h3>
                                </div>
                            </div>
                        </div>
                        @endif
                        @endforeach

                        {{-- <!--block 2-->
                        <div class="col-lg-4 col-md-12">
                            <div class="counter-outer-two">
                                <div class="tw-counter-media">
                                    <img src="frontend/images/icons/c-pic2.png" alt="">
                                </div>
                                <div class="icon-content">
                                    <div class="tw-count-number site-text-primary"><span
                                            class="counter site-text-primary">250</span>+</div>
                                    <h3 class="icon-content-info">On Time Delievery</h3>
                                </div>
                            </div>
                        </div>

                        <!--block 3-->
                        <div class="col-lg-4 col-md-12">
                            <div class="counter-outer-two">
                                <div class="tw-counter-media">
                                    <img src="frontend/images/icons/c-pic3.png" alt="">
                                </div>
                                <div class="icon-content">
                                    <div class="tw-count-number site-text-primary"><span
                                            class="counter site-text-primary">350</span>+</div>
                                    <h3 class="icon-content-info">Data-Driven Logistics Excellence</h3>
                                </div>
                            </div>
                        </div> --}}

                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- COMPANY APPROCH SECTION END -->

    <!-- OUR BLOG START -->
    <div class="section-full p-t120 p-b90 site-bg-gray bg-cover">
        <div class="container">

            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer" id="blogintro">
                <div class="wt-small-separator site-text-primary">
                    <div>Our Blogs</div>
                </div>
                @if(!empty($blogintro->title))
                <h2 class="wt-title">{{$blogintro->title}}</h2>
                <p class="section-head-text">
                   {{$blogintro->content}}
                </p>
                @endif
            </div>
            <!-- TITLE END-->

            <div class="section-content">
                <div class="row d-flex justify-content-center" id="blog">

                    <!-- Blog Post 1 -->
                    @foreach($posts as $post)
                    @if(!empty($post->title))
                    <div class="col-lg-4 col-md-6 col-sm-12 m-b30">
                        <div class="blog-post blog-post-4-outer">
                            <div class="wt-post-media wt-img-effect zoom-slow">
                                <a href="{{ route('blog-single', $post->slug) }}"><img src="{{asset('storage/'. $post->featured_image)}}" alt="Blog Image"></a>
                            </div>
                            <div class="wt-post-info">
                                <div class="wt-post-meta">
                                    <ul>
                                        <li class="post-date"><span>{{ \Carbon\Carbon::parse($post->created_at)->format('d') }}</span> {{ \Carbon\Carbon::parse($post->created_at)->format('M') }}</li>
                                    </ul>
                                </div>

                                <div class="wt-post-title">
                                    <h3 class="post-title"><a href="{{ route('blog-single', $post->slug) }}">{{$post->title}}</a></h3>
                                </div>
                                <div class="wt-post-text">
                                    <p>{!! $post->short_description !!}</p>
                                </div>
                                <div class="wt-post-readmore">
                                    <a href="{{ route('blog-single', $post->slug) }}" class="site-button-link site-text-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    @endforeach

                    {{-- <!-- Blog Post 2 -->
                    <div class="col-lg-4 col-md-6 col-sm-12 m-b30">
                        <div class="blog-post blog-post-4-outer">
                            <div class="wt-post-media wt-img-effect zoom-slow">
                                <a href="blog-single.html"><img src="frontend/images/blog/latest-2/l-1.jpg" alt="Blog Image"></a>
                            </div>
                            <div class="wt-post-info">
                                <div class="wt-post-meta">
                                    <ul>
                                        <li class="post-date"><span>08</span> Aug</li>
                                    </ul>
                                </div>

                                <div class="wt-post-title">
                                    <h3 class="post-title"><a href="blog-single.html">Sustainability in Logistics: Building a Greener Future</a></h3>
                                </div>
                                <div class="wt-post-text">
                                    <p>Learn how sustainable practices are being integrated into logistics operations to reduce environmental impact and improve efficiency.</p>
                                </div>
                                <div class="wt-post-readmore">
                                    <a href="blog-single.html" class="site-button-link site-text-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Blog Post 3 -->
                    <div class="col-lg-4 col-md-6 col-sm-12 m-b30">
                        <div class="blog-post blog-post-4-outer">
                            <div class="wt-post-media wt-img-effect zoom-slow">
                                <a href="blog-single.html"><img src="frontend/images/blog/latest/bg3.jpg" alt="Blog Image"></a>
                            </div>
                            <div class="wt-post-info">
                                <div class="wt-post-meta">
                                    <ul>
                                        <li class="post-date"><span>21</span> Aug</li>
                                    </ul>
                                </div>

                                <div class="wt-post-title">
                                    <h3 class="post-title"><a href="blog-single.html">The Future of Supply Chain Management: Key Trends to Watch</a></h3>
                                </div>
                                <div class="wt-post-text">
                                    <p>Explore the emerging trends in supply chain management and how businesses can stay ahead in a rapidly evolving logistics landscape.</p>
                                </div>
                                <div class="wt-post-readmore">
                                    <a href="blog-single.html" class="site-button-link site-text-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                    </div> --}}

                </div>
            </div>

        </div>
    </div>
    <!-- OUR BLOG END -->

    <!-- LATEST PRJECTS SLIDER START -->
    <div class="section-full  tw-project-1-wrap tw-project-1-wrap-bg site-bg-white">
        <!-- IMAGE CAROUSEL START -->
        <div class="section-content">

            <div class="container">
                <div class="tw-project-1-content">
                    <div class="tw-project-1-content-position">
                        <!-- TITLE START-->
                        <div class="section-head left wt-small-separator-outer">
                            <div class="wt-small-separator site-text-primary">
                                <div>Projects</div>
                            </div>
                            <h2>Featured Projects</h2>
                        </div>
                        <!-- TITLE END-->
                        <div class="owl-carousel project-carousel  owl-btn-bottom-left" id ="projects">
                            <!-- COLUMNS 1 -->
                            @foreach($projects as $project)
                            @if(!empty($project->name))
                            <div class="item">
                                <div class="project-new-2">
                                    <div class="wt-img-effect">
                                        <img src="{{asset('storage/'. $project->image)}}" alt="">
                                        <div class="project-view">
                                            <a class="elem pic-long project-view-btn" href="{{route('single-project', $project->slug)}}"
                                                title="Nh-16 Highway Bridge" data-lcl-txt="" data-lcl-author=""
                                                data-lcl-thumb="{{asset('storage/'. $project->image)}}">
                                                <i class="fa fa-search-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="project-new-content">
                                        <span class="project-new-category">{{$project->tag}}</span>
                                        <h4 class="wt-title"><a href="{{route('single-project', $project->slug)}}">{{$project->name}}</a></h4>
                                        <a href="{{route('single-project', $project->slug)}}" class="site-button-h-align">Read More</a>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @endforeach
                            {{-- <!-- COLUMNS 2 -->
                            <div class="item">
                                <div class="project-new-2">
                                    <div class="wt-img-effect">
                                        <img src="frontend/images/project/2.jpg" alt="">
                                        <div class="project-view">
                                            <a class="elem pic-long project-view-btn" href="frontend/images/project/2.jpg"
                                                title="Nh-16 Highway Bridge" data-lcl-txt="" data-lcl-author=""
                                                data-lcl-thumb="frontend/images/project/2.jpg">
                                                <i class="fa fa-search-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="project-new-content">
                                        <span class="project-new-category">Logistics, Analytics</span>
                                        <h4 class="wt-title"><a href="services-detail.html">Minimize Manufacturing</a>
                                        </h4>
                                        <a href="services-detail.html" class="site-button-h-align">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <!-- COLUMNS 3 -->
                            <div class="item">
                                <div class="project-new-2">
                                    <div class="wt-img-effect">
                                        <img src="frontend/images/project/3.jpg" alt="">
                                        <div class="project-view">
                                            <a class="elem pic-long project-view-btn" href="frontend/images/project/3.jpg"
                                                title="Nh-16 Highway Bridge" data-lcl-txt="" data-lcl-author=""
                                                data-lcl-thumb="frontend/images/project/3.jpg">
                                                <i class="fa fa-search-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="project-new-content">
                                        <span class="project-new-category">Warehousing , Distrbution</span>
                                        <h4 class="wt-title"><a href="services-detail.html">Warehouse inventory</a></h4>
                                        <a href="services-detail.html" class="site-button-h-align">Read More</a>
                                    </div>
                                </div>
                            </div> --}}


                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
    {{-- <script>
        document.querySelectorAll('.site-button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = button.dataset.tab;

                // Remove active class from all buttons
                document.querySelectorAll('.site-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to clicked button
                button.classList.add('active');

                // Toggle content visibility
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = content.dataset.tab === targetTab ? 'block' : 'none';
                });
            });
        });
        </script> --}}


    <script>
document.getElementById('shipment_type').addEventListener('change', function() {
    const shipmentType = this.value;
    const trackingTypeContainer = document.getElementById('trackingTypeContainer');
    const trackingTypeSelect = document.getElementById('tracking_type');
    const trackingInput = document.getElementById('tracking_number');
    const placeholders = JSON.parse(trackingInput.dataset.placeholders);

    // Reset fields
    trackingTypeContainer.style.display = 'none';
    trackingTypeSelect.innerHTML = '';
    trackingInput.placeholder = 'Select shipment type first';
    trackingInput.name = 'tracking_number';

    // Define tracking type options for each shipment type
    const typeOptions = {
        ocean: [
            { value: 'bl', label: 'Bill of Lading (B/L) Number' },
            { value: 'container', label: 'Container Number' }
        ],
        air: [
            { value: 'airway', label: 'Airway Bill Number' }
        ],
        lcl: [
            { value: 'bl', label: 'Bill of Lading Number' },
            { value: 'booking', label: 'Booking Number' }
        ],
        parcel: [
            { value: 'tracking', label: 'Parcel Tracking Number' }
        ]
    };

    // Populate tracking type selector
    if (typeOptions[shipmentType].length > 1) {
        trackingTypeContainer.style.display = 'block';
        typeOptions[shipmentType].forEach(option => {
            const opt = document.createElement('option');
            opt.value = option.value;
            opt.textContent = option.label;
            trackingTypeSelect.appendChild(opt);
        });
    }

    // Set initial tracking input properties
    updateTrackingInput(shipmentType, typeOptions[shipmentType][0].value);

    // Add listener for tracking type change
    trackingTypeSelect.addEventListener('change', function() {
        updateTrackingInput(shipmentType, this.value);
    });
});

function updateTrackingInput(shipmentType, trackingType) {
    const trackingInput = document.getElementById('tracking_number');
    const placeholders = JSON.parse(trackingInput.dataset.placeholders);

    // Construct the correct key based on shipment type
    let key;
    if (['air', 'parcel'].includes(shipmentType)) {
        // For types without sub-selection
        key = shipmentType;
    } else {
        // For types with sub-selection (ocean, lcl)
        key = `${shipmentType}_${trackingType}`;
    }

    // Update input properties
    trackingInput.placeholder = placeholders[key] || 'Enter tracking number';
    trackingInput.name = `${shipmentType}_${trackingType}_number`;

    // Set input patterns
    const patterns = {
        ocean_bl: '[A-Za-z]{2}\d{8}',
        ocean_container: '[A-Za-z]{4}\d{7}',
        air: '\d{3}-\d{8}',
        lcl_bl: '[A-Za-z]{3}\d{7}',
        lcl_booking: '[A-Za-z0-9]{10}',
        parcel: '[A-Za-z0-9]{18}'
    };

    trackingInput.pattern = patterns[key] || '.*';
    trackingInput.title = `Format: ${placeholders[key].match(/\((.+)\)/)?.[1] || 'varies'}`;
}
</script>

@endsection


