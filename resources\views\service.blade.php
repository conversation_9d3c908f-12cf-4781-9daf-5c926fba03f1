@extends('frontend.layouts.master')
@section('content')
    <!-- INNER PAGE BANNER -->
    <div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url(frontend/images/banner/1.jpg);">
        <div class="overlay-main site-bg-sky opacity-08"></div>
        <div class="container">
            <div class="wt-bnr-inr-entry">
                <div class="banner-title-outer">
                    <div class="banner-title-name">
                        <h2 class="wt-title">Our Services</h2>
                    </div>
                </div>
                <!-- BREADCRUMB ROW -->

                    <div>
                        <ul class="wt-breadcrumb breadcrumb-style-2">
                            <li><a href="/">Home</a></li>
                            <li>Our Services</li>
                        </ul>
                    </div>

                <!-- BREADCRUMB ROW END -->
            </div>
        </div>
    </div>
    <!-- INNER PAGE BANNER END -->

    <!-- SERVICES SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-gray tw-service-gallery-style3-area">
        <div class="services-gallery-block-outer3">
            <div class="container">
                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                <!-- TITLE START-->
                <div class="section-head center wt-small-separator-outer" id="serviceheading">
                    <div class="wt-small-separator site-text-primary">
                        <div>Our Services</div>
                    </div>
                    @if(!empty($serviceHeading->title))
                    <h2 class="wt-title">{{$serviceHeading->title}}</h2>
                    <p class="section-head-text">
                       {{$serviceHeading->content}}
                    </p>
                    @endif

                </div>
                <!-- TITLE END-->

                <div class="section-content" id="whatwedolist">
                    <div class="services-gallery-style3">
                        <div class="row">

                            <!-- Air Freight -->
                            @foreach($services as $i=> $service)
                            @if(!empty($service->name))
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="{{asset('storage/'. $service->icon)}}" alt="Air Freight">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">0{{$i+=1}}</span>
                                            <a href="{{route('service-single', $service->slug)}}">{{$service->name}}</a>
                                        </h3>
                                        <p>{!! $service->short_description !!}</p>
                                        <a href="{{route('service-single', $service->slug)}}" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @endforeach

                            {{-- <!-- Road Freight -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="frontend/images/icons/pic1.png" alt="Road Freight">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">02</span>
                                            <a href="services-detail.html">Road Freight</a>
                                        </h3>
                                        <p>Safe and efficient road transportation services for local and regional deliveries.</p>
                                        <a href="services-detail.html" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Ocean Freight -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="frontend/images/icons/pic2.png" alt="Ocean Freight">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">03</span>
                                            <a href="services-detail.html">Ocean Freight</a>
                                        </h3>
                                        <p>Cost-effective and secure sea freight solutions for bulk and large shipments.</p>
                                        <a href="services-detail.html" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Rail Freight -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="frontend/images/icons/pic4.png" alt="Rail Freight">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">04</span>
                                            <a href="services-detail.html">Rail Freight</a>
                                        </h3>
                                        <p>Reliable rail transportation for cost-effective and eco-friendly shipping.</p>
                                        <a href="services-detail.html" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Warehousing -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="frontend/images/icons/pic5.png" alt="Warehousing">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">05</span>
                                            <a href="services-detail.html">Warehousing</a>
                                        </h3>
                                        <p>Secure and strategically located warehouses for safe storage and inventory management.</p>
                                        <a href="services-detail.html" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Project Cargo -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="service-box-style3">
                                    <div class="service-media">
                                        <img src="frontend/images/icons/pic6.png" alt="Project Cargo">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title-large">
                                            <span class="service-title-large-number">06</span>
                                            <a href="services-detail.html">Project Cargo</a>
                                        </h3>
                                        <p>Tailored logistics solutions for heavy and oversized cargo transportation.</p>
                                        <a href="services-detail.html" class="site-button-2">View Details</a>
                                    </div>
                                </div>
                            </div> --}}

                        </div> <!-- End of Row -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SERVICES SECTION END -->

    <!-- Estimation SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-white tw-estimation-2-area">
        <div class="container">
            <div class="wt-separator-two-part">
                <div class="row wt-separator-two-part-row">
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-left">
                        <!-- TITLE START-->
                        <div class="section-head left wt-small-separator-outer" id="costheading">
                            <div class="wt-small-separator site-text-primary">
                                <div>Cost Estimation</div>
                            </div>
                            @if(!empty($costHeading->title))
                            <h2 class="wt-title">{{$costHeading->title}}</h2>
                            <p class="section-head-text">
                                {{$costHeading->content}}
                            </p>
                            @endif
                        </div>
                        <!-- TITLE END-->
                    </div>
                    <div class="col-xl-6 col-lg-6 col-md-12 wt-separator-two-part-right text-right">
                        <a href="/about-us" class="btn-half site-button"><span>Learn More</span><em></em></a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tw-estimation-2-section">
            <div class="container" id="servicesolution">
                <div class="row">
                    <!-- Solution & Expertise -->
                    @foreach($serviceSolutions as $i=> $solution)
                    @if(!empty($solution->title))
                    <div class="col-xl-4 col-lg-4 col-md-6">
                        <div class="tw-est-2-section-block">
                            <div class="tw-est-2-section-block-content">
                                <span class="tw-est-2-section-number">0{{$i+=1}}</span>
                                <div class="media">
                                    <img src="{{asset('storage/'. $solution->image)}}" alt="Solutions and Expertise">
                                </div>
                                <h3 class="tw-title">{{$solution->title}}</h3>
                                <p>{{$solution->content}}</p>
                                {{-- <a href="about-1.html" class="site-button-2-outline dark"><i class="fa fa-angle-right"></i></a> --}}
                            </div>
                        </div>
                    </div>
                    @endif
                    @endforeach

                    {{-- <!-- Multiple Warehouses -->
                    <div class="col-xl-4 col-lg-4 col-md-6">
                        <div class="tw-est-2-section-block">
                            <div class="tw-est-2-section-block-content">
                                <span class="tw-est-2-section-number">02</span>
                                <div class="media">
                                    <img src="frontend/images/estimation-icon/pic2.png" alt="Multiple Warehouses">
                                </div>
                                <h3 class="tw-title">Strategic Warehousing</h3>
                                <p>With multiple storage locations, we offer flexible warehousing solutions for efficient inventory management.</p>
                                <a href="about-1.html" class="site-button-2-outline dark"><i class="fa fa-angle-right"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- Tracking Made Easy -->
                    <div class="col-xl-4 col-lg-4 col-md-6">
                        <div class="tw-est-2-section-block active">
                            <div class="tw-est-2-section-block-content">
                                <span class="tw-est-2-section-number">03</span>
                                <div class="media">
                                    <img src="frontend/images/estimation-icon/pic3.png" alt="Tracking Made Easy">
                                </div>
                                <h3 class="tw-title">Real-Time Shipment Tracking</h3>
                                <p>Monitor your shipments in real time with our advanced tracking system, ensuring security and visibility.</p>
                                <a href="about-1.html" class="site-button-2-outline dark"><i class="fa fa-angle-right"></i></a>
                            </div>
                        </div>
                    </div> --}}

                </div> <!-- End of Row -->
            </div>
        </div>
    </div>

    <!-- Estimation SECTION END -->

    <!-- BOOKING SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-gray tw-booking-area" style="background-image: url(frontend/images/booking/bg-map.png);">

        <div class="container" id="quoteheading">
            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer">
                <div class="wt-small-separator site-text-primary">
                    <div>Request A Quote</div>
                </div>
                @if(!empty($quoteHeading->title))
                <h2 class="wt-title">{{$quoteHeading->title}}</h2>
                <p class="section-head-text">{{$quoteHeading->content}}</p>
                @endif
            </div>
            <!-- TITLE END-->
        </div>

        <div class="container">
            <div class="tw-booking-section">
                <div class="row">


                    <div class="col-xl-3 col-lg-3 col-md-12">
                        <div class="tw-booking-media">
                            <div class="media">
                                @if(!empty($quoteHeading->featured_image))
                                <img src="{{asset('storage/' .$quoteHeading->featured_image)}}" alt="#">
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-9 col-lg-9 col-md-12">
                        <div class="tw-booking-form">

                            <div class="row booking-tab-container">
                                <div class="col-lg-2 col-md-12 booking-tab-menu">
                                    <div class="list-group">
                                        <a href="#" class="list-group-item active text-center">
                                            <div class="media">
                                                <img src="frontend/images/booking/icon1.png" alt="">
                                            </div>
                                            <span>Request A Quote</span>
                                        </a>
                                        <a href="#" class="list-group-item text-center">
                                            <div class="media">
                                                <img src="frontend/images/booking/icon2.png" alt="">
                                            </div>
                                            <span>Track & Trace</span>
                                        </a>

                                    </div>
                                </div>
                                <div class="col-lg-10 col-md-12 booking-tab">
                                    <!-- flight section -->
                                    <div class="booking-tab-content active">
                                        <form action="{{ route('quotes.store') }}"  method="POST">
                                            @csrf
                                            <div class="row">

                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="username" type="text" required class="form-control" placeholder="Name">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="email" type="email" required class="form-control" placeholder="Email">
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4">
                                                    <div class="mb-3">
                                                        <input name="phone" type="text" required class="form-control" placeholder="Phone">
                                                    </div>
                                                </div>


                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <select id="Freight_Type" class="form-select" name="freight_type">
                                                            <option selected>Freight Type</option>
                                                            <option>Air</option>
                                                            <option>Sea</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <input name="city_of_departure" type="text" required class="form-control" placeholder="City Of Departure">
                                                    </div>
                                                </div>

                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <input name="delivery_city" type="text" required class="form-control" placeholder="Delivery City">
                                                    </div>
                                                </div>

                                                <div class="col-lg-6 col-md-6">
                                                    <div class="mb-3">
                                                        <select id="Incoterms" name="incoterms"  class="form-select">
                                                            <option selected>Incoterms</option>
                                                            <option>FOB</option>
                                                            <option>CIF</option>
                                                        </select>
                                                    </div>
                                                </div>


                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="weight" type="number" required class="form-control" placeholder="Weight">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="height" type="number" required class="form-control" placeholder="Height">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="width" type="number" required class="form-control" placeholder="Width">
                                                    </div>
                                                </div>
                                                <div class="col-lg-3 col-md-3">
                                                    <div class="mb-3">
                                                        <input name="length" type="number" required class="form-control" placeholder="Length">
                                                    </div>
                                                </div>

                                                <div class="col-lg-12">
                                                    <div class="tw-inline-checked mt-2 mb-3">
                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="fragile" value="0">
                                                            <input type="checkbox" class="form-check-input" id="exampleCheck1" name="fragile" value="1">
                                                            <label class="form-check-label" for="exampleCheck1">Fragile</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="express_delivery" value="0">
                                                            <input type="checkbox" class="form-check-input" id="exampleCheck2" name="express_delivery" value="1">
                                                            <label class="form-check-label" for="exampleCheck2">Express Delivery</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="insurance" value="0">
                                                            <input type="checkbox" class="form-check-input" id="exampleCheck3" name="insurance" value="1">
                                                            <label class="form-check-label" for="exampleCheck3">Insurance</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="hidden" name="packaging" value="0">
                                                            <input type="checkbox" class="form-check-input" id="exampleCheck4" name="packaging" value="1">
                                                            <label class="form-check-label" for="exampleCheck4">Packaging</label>
                                                        </div>
                                                    </div>
                                                </div>



                                                <div class="col-lg-12 col-md-12">
                                                    <div class="tw-booking-footer">
                                                        <div class="tw-booking-footer-btn">
                                                            <button type="submit" class="btn-half site-button">
                                                                <span>Submit Now</span><em></em>
                                                            </button>
                                                        </div>
                                                        <span class="tw-booking-footer-text">Quote</span>
                                                    </div>

                                                </div>

                                            </div>

                                        </form>
                                    </div>
                                    <!-- train section -->
                                    <div class="booking-tab-content">
                                        <form class="track-and-trace-form" method="GET" action="{{ route('track.shipment') }}">
                                            @csrf
                                            <div class="row">

                                                {{-- <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <select id="Shipment_Type" class="form-select">
                                                            <option selected>Shipment Type</option>
                                                            <option>Road</option>
                                                            <option>Train</option>
                                                            <option>Air</option>
                                                            <option>Sea</option>
                                                        </select>
                                                    </div>
                                                </div> --}}
                                                @if(session('error'))
                                                    <div class="alert alert-danger">
                                                        {{ session('error') }}
                                                    </div>
                                                @endif

                                                <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <input type="email" name="email" class="form-control" id="email" rows="3" placeholder="Enter your email" required>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 col-md-12">
                                                    <div class="mb-3">
                                                        <input type="text" name="chassis_number" class="form-control" id="chassis_number" rows="3" placeholder="Enter Plate Number" required></input>
                                                    </div>
                                                </div>


                                                {{-- <div class="col-lg-12">
                                                    <div class="tw-inline-checked mt-2 mb-3">
                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input" id="Fragile1">
                                                            <label class="form-check-label" for="Fragile1">Fragile</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input" id="Express2">
                                                            <label class="form-check-label" for="Express2">Express Delivery</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input" id="Insurance3">
                                                            <label class="form-check-label" for="Insurance3">Insurance</label>
                                                        </div>

                                                        <div class="mb-4 form-check">
                                                            <input type="checkbox" class="form-check-input" id="packaging4">
                                                            <label class="form-check-label" for="packaging4">Packaging</label>
                                                        </div>
                                                    </div>
                                                </div> --}}

                                                <div class="col-lg-12 col-md-12">
                                                    <div class="tw-booking-footer">
                                                        <div class="tw-booking-footer-btn">
                                                            <button type="submit" class="btn-half site-button">
                                                                <span>Track & Trace</span><em></em>
                                                            </button>
                                                        </div>
                                                        <span class="tw-booking-footer-text">Trace</span>
                                                    </div>

                                                </div>

                                            </div>

                                        </form>
                                    </div>


                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- BOOKING SECTION END -->
@endsection
