<?php

namespace App\Filament\Resources\CostEstimationHeadingResource\Pages;

use App\Filament\Resources\CostEstimationHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCostEstimationHeadings extends ListRecords
{
    protected static string $resource = CostEstimationHeadingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
