
@extends('frontend.layouts.master')
@section('content')
<div class="page-content">

    <!-- INNER PAGE BANNER -->
    <div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url({{ asset('frontend/images/banner/1.jpg')}});">
        <div class="overlay-main site-bg-sky opacity-08"></div>
        <div class="container">
            <div class="wt-bnr-inr-entry">
                <div class="banner-title-outer">
                    <div class="banner-title-name">
                        <h2 class="wt-title">News Detail</h2>
                    </div>
                </div>
                <!-- BREADCRUMB ROW -->

                    <div>
                        <ul class="wt-breadcrumb breadcrumb-style-2">
                            <li><a href="/">Home</a></li>
                            <li>News Detail</li>
                        </ul>
                    </div>

                <!-- BREADCRUMB ROW END -->
            </div>
        </div>
    </div>
    <!-- INNER PAGE BANNER END -->



<!-- OUR BLOG START -->
<div class="section-full  p-t120 p-b90 bg-white">
    <div class="container">

        <!-- BLOG SECTION START -->
        <div class="section-content">
            <div class="row d-flex justify-content-center">

                <div class="col-lg-8 col-md-12">
                    <!-- BLOG START -->
                    <div class="blog-post-single-outer">
                        <div class="blog-post-single bg-white">

                            <div class="wt-post-info">

                                <div class="wt-post-media m-b30">
                                    <img src="{{ asset('storage/'. $news->featured_image) }}" alt="">
                                </div>

                                <div class="wt-post-title ">
                                    <div class="wt-post-meta-list">
                                        <div class="wt-list-content post-date">{{ \Carbon\Carbon::parse($news->created_at)->format('F d, Y') }}</div>
                                        {{-- <div class="wt-list-content post-comment">Comment 20</div>
                                        <div class="wt-list-content post-view">Views 250</div> --}}
                                    </div>
                                    <h3 class="post-title">{{ $news->title }}</h3>

                                </div>

                                <div class="wt-post-discription">

                                    <p>
                                        {!! $news->description !!}
                                    </p>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-6 col-sm-6">
                                            <div class="one-column1 mb-3">
                                                <div class="wt-media">
                                                    <img src="{{ asset('storage/'. $news->image)}}" alt="" class="img-responsive">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 col-md-6 col-sm-6">
                                            <div class="one-column2 mb-3">
                                                <div class="wt-media">
                                                    <img src="{{ asset('storage/'. $news->featured_image)}}" alt="" class="img-responsive">
                                                </div>
                                            </div>
                                        </div>

                                  </div>

                                </div>
                            </div>



                        </div>

                        <div class="post-area-tags-wrap">
                            <div class="row">
                                <div class="col-lg-7 col-md-12">

                                </div>
                                <div class="col-lg-5 col-md-12">
                                    <div class="post-social-icons-wrap">
                                        <h4 class="section-head-small mb-4">Share News</h4>
                                        <ul class="post-social-icons">
                                            <li>
                                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('blog-single', $news->slug)) }}"
                                                   target="_blank"
                                                   rel="noopener noreferrer"
                                                   class="fa fa-facebook"
                                                   aria-label="Share on Facebook">
                                                </a>
                                            </li>
                                            <li>
                                                <a href="https://twitter.com/intent/tweet?text={{ urlencode($news->title) }}&url={{ urlencode(route('blog-single', $news->slug)) }}"
                                                   target="_blank"
                                                   rel="noopener noreferrer"
                                                   class="fa fa-twitter"
                                                   aria-label="Share on Twitter">
                                                </a>
                                            </li>
                                            <li>
                                                <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ urlencode(route('blog-single', $news->slug)) }}&title={{ urlencode($news->title) }}&summary={{ urlencode($news->description) }}"
                                                   target="_blank"
                                                   rel="noopener noreferrer"
                                                   class="fa fa-linkedin"
                                                   aria-label="Share on LinkedIn">
                                                </a>
                                            </li>
                                            <li>
                                                <a href="https://api.whatsapp.com/send?text={{ urlencode($news->title . ' ' . route('blog-single', $news->slug)) }}"
                                                   target="_blank"
                                                   rel="noopener noreferrer"
                                                   class="fa fa-whatsapp"
                                                   aria-label="Share on WhatsApp">
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="post-navigation m-t30">
                            <div class="post-nav-links">
                                <div class="post-nav-item nav-post-prev">
                                    <div class="nav-post-arrow">
                                        <a href="javascript:;">
                                            <i class="fa fa-angle-left"></i>
                                        </a>
                                    </div>
                                    <div class="nav-post-meta">
                                        @if($previous)
                                            <label>Prev News</label>
                                            <a href="{{ route('news-single', $previous->slug) }}">{{ $previous->title }}</a>
                                        @else
                                            <label>No Previous</label>
                                            <a href="javascript:;">You're viewing the oldest post</a>
                                        @endif
                                    </div>
                                </div>
                                <div class="post-nav-item nav-post-next">
                                    <div class="nav-post-arrow">
                                        <a href="javascript:;">
                                            <i class="fa fa-angle-right"></i>
                                        </a>
                                    </div>
                                    <div class="nav-post-meta">
                                        @if($next)
                                            <label>Next News</label>
                                            <a href="{{ route('news-single', $next->slug) }}">{{ $next->title }}</a>
                                        @else
                                            <label>No Newer Posts</label>
                                            <a href="javascript:;">You're viewing the latest post</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-12 rightSidebar side-bar">
                    <div class="widget search-bx">

                        <form role="search" action="{{route('search')}}" method="GET">
                            <div class="input-group">
                                <input class="form-control" value="" name="q" type="search" placeholder="Type to search"/>
                                <button class="btn" type="submit" id="button-addon2"><i class="fa fa-search"></i></button>
                            </div>
                        </form>

                    </div>

                    <div class="widget all_services_list">
                        <h4 class="section-head-small mb-4">Transport Services</h4>
                        <div class="all_services m-b30">
                            <ul>
                                @foreach($services as $service)
                                @if(!empty($service->name))
                                <li><a href="{{route('service-single', $service->slug)}}">{{$service->name}}</a></li>
                                @endif
                                @endforeach
                            </ul>
                        </div>
                    </div>

                    <div class="widget recent-posts-entry">
                        <h4 class="section-head-small mb-4">Popular Post</h4>
                        <div class="section-content">
                            <div class="widget-post-bx">
                                @foreach($posts as $post)
                                @if(!empty($post->title))
                                <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="{{asset('storage/'.$post->featured_image)}}" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">{{ \Carbon\Carbon::parse($post->created_at)->format('F d, Y') }}</span>
                                            <span class="post-title">
                                                <a href="{{ route('blog-single', $post->slug) }}">{{ Str::limit(strip_tags($post->short_description), 60, '...') }}</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>



                    <div class="widget tw-sidebar-gallery-wrap">
                        <h4 class="section-head-small mb-4">Gallery</h4>
                        <div class="tw-sidebar-gallery">
                            <ul>
                                @foreach($galleryImages->take(6) as $image)
                                @if(!empty($image->image))
                                <li>
                                    <div class="tw-service-gallery-thumb">
                                        <a class="elem" href="frontend/images/gallery/thumb/pic1.jpg" title="Title 1" data-lcl-author="" data-lcl-thumb="frontend/images/gallery/thumb/pic1.jpg">
                                            <img src="{{asset('storage/'.$image->image)}}" alt="">
                                            <i class="fa fa-file-image-o"></i>
                                        </a>
                                    </div>
                                </li>
                                @endif
                                @endforeach
                            </ul>

                        </div>
                    </div>
                    <div class="widget tw-contact-bg-section">
                        <h4 class="section-head-small mb-4">Any Emergency?</h4>
                        <div class="tw-contact-bg-inner" style="background-image: url({{ asset('frontend/images/background/bg-4.jpg')}});">
                           <div class="section-top">
                               <span>Call Our 24/7 Customer Support</span>
                               @if(!empty($contact->phone))
                               <h3 class="tw-con-number"><a href="tel:+9(465)3212055">{{$contact->phone}}</a></h3>
                               @endif
                           </div>
                           <div class="section-bot">
                               <ul>
                                <li>
                                    <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    @if(!empty($contact->address))
                                    <p>{{$contact->address}}</p>
                                    @endif
                                </li>
                                <li>
                                    <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    @if(!empty($contact->email))
                                    <p>{{$contact->email}}</p>
                                    @endif
                                </li>
                               </ul>
                           </div>
                        </div>
                    </div>


                </div>

            </div>

        </div>

    </div>

</div>
<!-- OUR BLOG END -->


</div>
@endsection
