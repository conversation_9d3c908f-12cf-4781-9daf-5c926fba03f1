<?php

namespace App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource\Pages;

use App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListGalleryHeadings extends ListRecords
{
    protected static string $resource = GalleryHeadingResource::class;

    public function getTitle(): string
    {
        return 'Gallery Headnig';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
