<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->string('chassis_number')->unique();
            $table->string('shipment_type');
            $table->text('description');
            $table->string('origin');
            $table->string('destination');
            $table->string('weight');
            $table->string('dimension');
            $table->string('quantity');
            $table->string('price');
            $table->string('currency');
            $table->string('pickup_date');
            $table->string('delivery_date');
            $table->string('pickup_time');
            $table->string('delivery_time');
            $table->string('pickup_address');
            $table->string('delivery_address');
            $table->string('pickup_contact');
            $table->string('delivery_contact');
            $table->string('pickup_email');
            $table->string('delivery_email');
            $table->string('pickup_phone');
            $table->string('delivery_phone');
            $table->string('pickup_instruction');
            $table->string('delivery_instruction');
            $table->boolean('fragile')->default(false);
            $table->boolean('express_delivery')->default(false);
            $table->boolean('insurance')->default(false);
            $table->boolean('packaging')->default(false);
            $table->string('status')->default('In Transit');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
