<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;

class EditServiceSolution extends EditRecord
{
    protected static string $resource = ServiceSolutionResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Edit Solution';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
