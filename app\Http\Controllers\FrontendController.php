<?php

namespace App\Http\Controllers;

use App\Models\AboutCompany;
use App\Models\Approach;
use App\Models\BlogIntro;
use App\Models\BlogPost;
use App\Models\Contact;
use App\Models\CostEstimationHeading;
use App\Models\EstimationIntro;
use App\Models\GalleryHeading;
use App\Models\GalleryImage;
use App\Models\MissionVission;
use App\Models\News;
use App\Models\NewsletterHeading;
use App\Models\Overview;
use App\Models\Partner;
use App\Models\Project;
use App\Models\QuoteHeading;
use App\Models\RequestQuote;
use App\Models\Service;
use App\Models\ServiceHeading;
use App\Models\ServiceIntro;
use App\Models\ServiceSolution;
use App\Models\SiteIdentity;
use App\Models\Slider;
use App\Models\Solution;
use App\Models\TestimonialsContent;
use App\Models\TestimonialsProfile;
use App\Models\WhatWeDo;
use App\Models\WhatWeDoList;
use App\Models\WhyChooseUs;
use Illuminate\Http\Request;

class FrontendController extends Controller
{
    public function getWhatWeDoHeading(){
        $whatwedo = WhatWeDo::first();
        return $whatwedo;
    }

    public function getWhatWeDoList(){
        $whatwedolist = WhatWeDoList::all();
        return $whatwedolist;
    }

    public function getWhyChooseUs(){
        $whychooseus = WhyChooseUs::first();
        return $whychooseus;
    }

    public function getRequestQuoteHeading(){
        $requestquoteheading = RequestQuote::first();
        return $requestquoteheading;
    }

    public function getAboutCompany(){
        $aboutcompany = AboutCompany::all();
        return $aboutcompany;
    }

    public function getEstimationHeading(){
        $estimationheading = EstimationIntro::first();
        return $estimationheading;
    }

    public function getSolution(){
        $solutions = Solution::all();
        return $solutions;
    }

    public function getTestimonialProfile(){
        $testimonialprofile = TestimonialsProfile::all();
        return $testimonialprofile;
    }

    public function getTestimonialContent(){
        $testimonialcontent = TestimonialsContent::all();
        return $testimonialcontent;
    }

    public function getPartner(){
        $partners = Partner::all();
        return $partners;
    }

    public function getServiceIntro(){
        $serviceintro = ServiceIntro::first();
        return $serviceintro;
    }

    public function getService(){
        $services = Service::all();
        return $services;
    }

    public function serviceDetail(Service $service, $slug){
        $service = Service::where('slug', $slug)->firstOrFail();
        $services = Service::all();
        $siteId = SiteIdentity::first();
        $contact = Contact::first();
        $posts = BlogPost::latest()->get();
        $galleryImages = GalleryImage::latest()->get();
        $subscriptionHeading = NewsletterHeading::first();
        return view('service-single', compact('service', 'services', 'siteId', 'contact', 'subscriptionHeading', 'posts', 'galleryImages'));
    }

    public function getBlogIntro(){
        $blogintro = BlogIntro::first();
        return $blogintro;
    }


    public function getBlog(){
        $blog = BlogPost::all();
        return $blog;
    }

    public function blogDetail(BlogPost $post, $slug){

        $post = BlogPost::where('slug', $slug)->firstOrFail();
        $previous = BlogPost::where('id', '<', $post->id)
        ->orderBy('id', 'desc')
        ->first();

        $next = BLogPost::where('id', '>', $post->id)
        ->orderBy('id', 'asc')
        ->first();
        $services = Service::all();
        $posts = BlogPost::latest()->get();
        $galleryImages = GalleryImage::latest()->get();
        $siteId = SiteIdentity::first();
        $contact = Contact::first();
        $subscriptionHeading = NewsletterHeading::first();
        return view('blog-single', compact('post', 'galleryImages', 'posts', 'siteId', 'contact', 'subscriptionHeading', 'previous', 'next', 'services'));
    }

    public function getProject(){
        $projects = Project::all();
        return $projects;
    }

    public function projectDetail(Project $project, $slug){
        $project = Project::where('slug', $slug)->firstOrFail();
        $services = Service::all();
        $siteId = SiteIdentity::first();
        $contact = Contact::first();
        $posts = BlogPost::latest()->get();
        $galleryImages = GalleryImage::latest()->get();
        $subscriptionHeading = NewsletterHeading::first();
        return view('single-project', compact('project', 'services', 'siteId', 'contact', 'posts', 'galleryImages', 'subscriptionHeading'));
    }

    public function getApproach(){
        $approaches = Approach::all();
        return $approaches;
    }

    public function getSiteId(){
        $siteId = SiteIdentity::first();
        return $siteId;
    }

    public function getOverView(){
        $overview = Overview::first();
        return $overview;
    }

    public function getMission(){
        $mission = MissionVission::first();
        return $mission;
    }

    public function getServiceHeading(){
        $serviceHeading = ServiceHeading::first();
        return $serviceHeading;
    }

    public function getSlider(){
        $slider = Slider::all();
        return $slider;
    }

    public function getCostEstimationHeading(){
        $costHeading = CostEstimationHeading::first();
        return $costHeading;
    }

    public function getServiceSolution(){
        $serviceSolution = ServiceSolution::all();
        return $serviceSolution;
    }

    public function getQuoteHeading(){
        $quoteHeading = QuoteHeading::first();
        return $quoteHeading;
    }

    public function getGalleryHeading(){
        $galleryHeading = GalleryHeading::first();
        return $galleryHeading;
    }

    public function getGalleryImage(){
        $gallery = GalleryImage::all();
        return $gallery;
    }

    public function getNews(){
        $news = News::all();
        return $news;
    }

    public function NewsDetail(News $news, $slug){
        $news = News::where('slug', $slug)->firstOrFail();
        $previous = News::where('id', '<', $news->id)
        ->orderBy('id', 'desc')
        ->first();

        $next = News::where('id', '>', $news->id)
        ->orderBy('id', 'asc')
        ->first();
        $services = Service::all();
        $services = Service::all();
        $siteId = SiteIdentity::first();
        $contact = Contact::first();
        $posts = BlogPost::latest()->get();
        $galleryImages = GalleryImage::latest()->get();
        $subscriptionHeading = NewsletterHeading::first();
        return view('news-detail', compact('news', 'siteId', 'contact', 'posts', 'galleryImages', 'subscriptionHeading', 'services', 'previous', 'next'));
    }

    public function getContact(){
        $contact = Contact::first();
        return $contact;
    }

    public function getSubscriptionHeading(){
        $subscriptionHeading = NewsletterHeading::first();
        return $subscriptionHeading;
    }

}
