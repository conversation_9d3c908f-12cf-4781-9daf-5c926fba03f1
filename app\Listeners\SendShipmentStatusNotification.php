<?php

namespace App\Listeners;

use App\Events\ShipmentStatusChanged;
use App\Mail\ShipmentStatusChangedNotification;
use App\Models\ShipmentEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendShipmentStatusNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ShipmentStatusChanged $event): void
    {
        $shipment = $event->shipment;
        // $email = "<EMAIL>";
        // Mail::to($email)->send(new ShipmentStatusChangedNotification($shipment));
        //Get all emails linked to the chassis number
        $emails = ShipmentEmail::where('chassis_number', $shipment->chassis_number)->pluck('email');

        // Send email notifications
        foreach ($emails as $email) {
            Mail::to($email)->send(new ShipmentStatusChangedNotification($shipment));
        }
    }
}
