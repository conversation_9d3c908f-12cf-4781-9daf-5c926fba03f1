<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use Sluggable;
    protected $fillable = [
        'name',
        'tag',
        'short_description',
        'description',
        'client_name',
        'project_detail',
        'project_detail_image1',
        'project_detail_image2',
        'image',
        'slug',
    ];

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name',
            ],
        ];
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
