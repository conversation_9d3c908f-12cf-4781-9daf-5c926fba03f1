<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\RequestQuoteResource\Pages;
use App\Filament\Resources\RequestQuoteResource\RelationManagers;
use App\Models\RequestQuote;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RequestQuoteResource extends Resource
{
    protected static ?string $model = RequestQuote::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $cluster = HomePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'Request Quotes Heading';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')->required()->label('Title'),
                Forms\Components\TextInput::make('content')->required()->label('Content'),
                Forms\Components\FileUpload::make('featured_image')->required()->label('Featured Image'),
                Forms\Components\FileUpload::make('bg_image')->required()->label('Background Image'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')->searchable()->sortable()->label('Title'),
                Tables\Columns\TextColumn::make('content')->searchable()->sortable()->label('Content'),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image')->sortable(),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRequestQuotes::route('/'),
            'create' => Pages\CreateRequestQuote::route('/create'),
            'edit' => Pages\EditRequestQuote::route('/{record}/edit'),
        ];
    }
}
