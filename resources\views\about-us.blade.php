@extends('frontend.layouts.master')
@section('content')
<div class="page-content">

    <!-- INNER PAGE BANNER -->
    <div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url({{ asset('frontend/images/banner/1.jpg')}});">
        <div class="overlay-main site-bg-sky opacity-08"></div>
        <div class="container">
            <div class="wt-bnr-inr-entry">
                <div class="banner-title-outer">
                    <div class="banner-title-name">
                        <h2 class="wt-title">About Us</h2>
                    </div>
                </div>
                <!-- BREADCRUMB ROW -->

                    <div>
                        <ul class="wt-breadcrumb breadcrumb-style-2">
                            <li><a href="/">Home</a></li>
                            <li>About Us</li>
                        </ul>
                    </div>

                <!-- BREADCRUMB ROW END -->
            </div>
        </div>
    </div>
    <!-- INNER PAGE BANNER END -->

    <!-- WHAT WE DO SECTION START -->
    <div class="section-full p-t120 p-b90 site-bg-gray tw-what-wedo-area">

        <div class="container">

            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer">
                <div class="wt-small-separator site-text-primary">
                   <div>What we do!</div>
                </div>
                <h2 class="wt-title">{{$whatwedo->title}}</h2>
                <p class="section-head-text">{{$whatwedo->content}}</p>
            </div>
            <!-- TITLE END-->

            <div class="tw-what-wedo-section">

                <div class="row">
                    <div class="col-xl-5 col-lg-5 col-md-12">
                        <div class="tw-what-wedo-media">
                            <img src="{{ asset('storage/'. $whatwedo->featured_image) }}" alt="">
                        </div>
                    </div>

                    <div class="col-xl-7 col-lg-7 col-md-12">
                        <div class="tw-service-icon-box-wrap">
                            @foreach($services->take(3) as $i=> $service)
                            @if(!empty($service->name))
                            <div class="service-icon-box-two">

                                <div class="service-icon-box-two-media">
                                    <img src="{{ asset('storage/'. $service->icon) }}" alt="Road Freight">
                                </div>

                                <div class="service-icon-box-title">
                                    <h3 class="wt-title">
                                        <a href="{{route('service-single', $service->slug)}}">
                                            <span class="site-text-primary">0{{$i+=1}}.</span> {{$service->name}}
                                        </a>
                                    </h3>
                                    <p>
                                        {!! $service->short_description !!}
                                    </p>
                                </div>

                            </div>
                            @endif
                            @endforeach
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
    <!-- WHAT WE DO SECTION END -->


    <!-- ABOUT US -->
    <div class="section-full p-t120 p-b90 site-bg-gray bg-cover">
        <div class="container">

            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer">
                <div class="wt-small-separator site-text-primary">
                   <div>Our Mission</div>
                </div>
           </div>
            <!-- TITLE END-->


            <div class="section-content">
                <div class="row d-flex justify-content-center">
                    {!! $mission->mission_vission !!}
                </div>
            </div>

        </div>
    </div>

    <div class="section-full p-t120 p-b90 site-bg-gray bg-cover">
        <div class="container">

            <!-- TITLE START-->
            <div class="section-head center wt-small-separator-outer">
                <div class="wt-small-separator site-text-primary">
                   <div>Overview</div>
                </div>
           </div>
            <!-- TITLE END-->


            <div class="section-content">
                <div class="row d-flex justify-content-center">
                    {!! $overview->overview !!}
                </div>
            </div>

        </div>
    </div>

     <!-- WE ACHIVED SECTION START -->
     <div class="section-full site-bg-dark tw-we-achived">

        <div class="container">

            <div class="tw-we-achived-section">

                @foreach($aboutcompany->take(3) as $list)
                @if(!empty($list->title))
                <div class="tw-we-achived-box-warp {{ $loop->iteration == 2 ? 'bg-skew' : '' }}">
                    <div class="tw-we-achived-box">
                        <h2 class="counter">{{$list->counter}}</h2>
                        <span>{{$list->title}}</span>
                    </div>
                </div>
                @endif
                @endforeach
            </div>
        </div>

    </div>
    <!-- WE ACHIVED SECTION END -->
</div>
@endsection
