<?php

namespace App\Filament\Resources\TestimonialsContentResource\Pages;

use App\Filament\Resources\TestimonialsContentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTestimonialsContents extends ListRecords
{
    protected static string $resource = TestimonialsContentResource::class;

    public function getTitle(): string
    {
        return 'Testimonial Contents';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
