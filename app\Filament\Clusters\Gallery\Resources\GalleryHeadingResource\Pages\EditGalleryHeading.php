<?php

namespace App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource\Pages;

use App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditGalleryHeading extends EditRecord
{
    protected static string $resource = GalleryHeadingResource::class;

    public function getTitle(): string
    {
        return 'Edit Gallery Headnig';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
