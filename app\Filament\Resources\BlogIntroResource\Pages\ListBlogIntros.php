<?php

namespace App\Filament\Resources\BlogIntroResource\Pages;

use App\Filament\Resources\BlogIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;

class ListBlogIntros extends ListRecords
{
    protected static string $resource = BlogIntroResource::class;

    public function getTitle(): string
    {
        return 'Blog Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
