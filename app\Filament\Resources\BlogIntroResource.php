<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\Blog;
use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\BlogIntroResource\Pages;
use App\Filament\Resources\BlogIntroResource\RelationManagers;
use App\Models\BlogIntro;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BlogIntroResource extends Resource
{
    protected static ?string $model = BlogIntro::class;

    protected static ?string $navigationIcon = 'heroicon-s-pencil-square';
    protected static ?string $cluster = Blog::class;

    public static function getNavigationLabel(): string
    {
        return 'Blog Heading';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('content')
                    ->label('Content')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlogIntros::route('/'),
            'create' => Pages\CreateBlogIntro::route('/create'),
            'edit' => Pages\EditBlogIntro::route('/{record}/edit'),
        ];
    }
}
