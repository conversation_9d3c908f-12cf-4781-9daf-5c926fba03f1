<?php

namespace App\Filament\Resources\MissionVissionResource\Pages;

use App\Filament\Resources\MissionVissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMissionVissions extends ListRecords
{
    protected static string $resource = MissionVissionResource::class;

    public function getTitle():string
    {
        return 'Mission And Vission';
    }
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
