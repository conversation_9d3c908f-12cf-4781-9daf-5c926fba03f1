<?php

namespace App\Filament\Resources\NewsletterHeadingResource\Pages;

use App\Filament\Resources\NewsletterHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditNewsletterHeading extends EditRecord
{
    protected static string $resource = NewsletterHeadingResource::class;

    public function getTitle():string
    {
        return 'Edit Subscription Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
