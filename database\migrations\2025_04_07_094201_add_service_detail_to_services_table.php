<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->string('description_title')->after('short_description')->nullable();
            $table->text('service_offered')->after('description')->nullable();
            $table->string('service_offered_image')->after('service_offered')->nullable();
            $table->string('icon')->after('service_offered_image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('detail_title');
            $table->dropColumn('service_offered');
            $table->dropColumn('service_offered_image');
            $table->dropColumn('icon');
        });
    }
};
