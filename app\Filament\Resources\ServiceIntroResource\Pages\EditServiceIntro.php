<?php

namespace App\Filament\Resources\ServiceIntroResource\Pages;

use App\Filament\Resources\ServiceIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;

class EditServiceIntro extends EditRecord
{
    protected static string $resource = ServiceIntroResource::class;

    public function getTitle(): string
    {
        return 'Edit Service Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
