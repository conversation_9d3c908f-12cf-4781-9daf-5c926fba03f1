@extends('frontend.layouts.master')
@section('content')

<div class="page-content">

    <!-- INNER PAGE BANNER -->
<div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url({{ asset('frontend/images/banner/1.jpg')}});">
        <div class="overlay-main site-bg-sky opacity-08"></div>
        <div class="container">
            <div class="wt-bnr-inr-entry">
                <div class="banner-title-outer">
                    <div class="banner-title-name">
                        <h2 class="wt-title">Services Detail</h2>
                    </div>
                </div>
                <!-- BREADCRUMB ROW -->

                    <div>
                        <ul class="wt-breadcrumb breadcrumb-style-2">
                            <li><a href="index.html">Home</a></li>
                            <li>Services Detail</li>
                        </ul>
                    </div>

                <!-- BREADCRUMB ROW END -->
            </div>
        </div>
    </div>
    <!-- INNER PAGE BANNER END -->

    <!-- SERVICE DETAIL SECTION START -->
    <div class="section-full p-t120 p-b90 bg-white">

        <div class="container">
            <div class="row">

                <div class="col-lg-8 col-md-12">
                    <div class="service-full-info">
                        <div class="services-etc m-b30">
                            <div class="service-category-title">
                                <div class="service-category-media"><img src="{{ asset('storage/'. $service->icon)}}" alt="#"></div>
                                <h2>{{$service->name}}</h2>
                            </div>
                            <div class="wt-media">
                                <img src="{{'storage/'. $service->image}}" alt="">
                            </div>

                            <h2 class="wt-title mt-4 mb-4">{{$service->description_title}}</h2>
                            <p>
                                {!! $service->description !!}
                            </p>
                            {{-- <p>
                                Master-builder of human happiness. No one rejects, dislikes, or avoids pleasure itself, because pleasure, but because those who do not know how to pursue pleasure rationally encounterconsequences that  are extremely painful. Nor again is there anyone.
                            </p> --}}

                            <h2 class="wt-title mt-4 mb-4">Services offered</h2>
                            <div class="service-offered">
                                <div class="media">
                                    <img class="img" src="{{ asset('storage/'. $service->service_offered_image)}}" alt="#">
                                </div>
                                <div class="service-offered-list">
                                    {{-- <p>
                                        No one rejects, dislikes, or avoids pleasure itself, because pleasure, but because those who do not know how to pursue.
                                    </p> --}}
                                    <div class="tw-checked-list">
                                        {{-- <ul>
                                            <li>Cargo Security Management</li>
                                            <li>Warehouing</li>
                                            <li>Custos Borkerage</li>
                                            <li>Unlimited Transfer</li>
                                            <li>Inventory Management</li>
                                            <li>Short Team Storage</li>
                                            <li>Shrink Wrapping</li>
                                        </ul> --}}
                                        {!! $service->service_offered !!}
                                    </div>


                                </div>
                            </div>

                            <h2 class="wt-title mt-4 mb-4">Book other services</h2>

                            <div class="book-other-services site-bg-sky-blue-light p-5">
                                <form action="{{ route('quotes.store') }}" method="POST">
                                    @csrf
                                    <div class="row">

                                        <div class="col-lg-4 col-md-4">
                                            <div class="mb-3">
                                                <input name="username" type="text" required
                                                    class="form-control" placeholder="Name">
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-4">
                                            <div class="mb-3">
                                                <input name="email" type="email" required
                                                    class="form-control" placeholder="Email">
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-4">
                                            <div class="mb-3">
                                                <input name="phone" type="text" required
                                                    class="form-control" placeholder="Phone">
                                            </div>
                                        </div>


                                        <div class="col-lg-6 col-md-6">
                                            <div class="mb-3">
                                                <select id="Freight_Type" name="freight_type" class="form-select">
                                                    <option selected>Freight Type</option>
                                                    <option>Air</option>
                                                    <option>Sea</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 col-md-6">
                                            <div class="mb-3">
                                                <input name="city_of_departure" type="text" required
                                                    class="form-control" placeholder="City Of Departure">
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-6">
                                            <div class="mb-3">
                                                <input name="delivery_city" type="text" required
                                                    class="form-control" placeholder="Delivery City">
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-6">
                                            <div class="mb-3">
                                                <select id="Incoterms" name="incoterms" class="form-select">
                                                    <option selected>Incoterms</option>
                                                        <option>FOB</option>
                                                        <option>CIF</option>
                                                </select>
                                            </div>
                                        </div>


                                        <div class="col-lg-3 col-md-3">
                                            <div class="mb-3">
                                                <input name="weight" type="number" required
                                                    class="form-control" placeholder="Weight">
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-3">
                                            <div class="mb-3">
                                                <input name="height" type="number" required
                                                    class="form-control" placeholder="Height">
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-3">
                                            <div class="mb-3">
                                                <input name="width" type="number" required
                                                    class="form-control" placeholder="Width">
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-3">
                                            <div class="mb-3">
                                                <input name="length" type="number" required
                                                    class="form-control" placeholder="Length">
                                            </div>
                                        </div>

                                        <div class="col-lg-12">
                                            <div class="tw-inline-checked mt-2 mb-3">
                                                <div class="mb-4 form-check">
                                                    <input type="hidden" name="fragile" value="0">
                                                    <input type="checkbox" class="form-check-input" name="fragile" value="1"
                                                        id="exampleCheck1">
                                                    <label class="form-check-label"
                                                        for="exampleCheck1">Fragile</label>
                                                </div>

                                                <div class="mb-4 form-check">
                                                    <input type="hidden" name="express_delivery" value="0">
                                                    <input type="checkbox" class="form-check-input" name="express_delivery" value="1"
                                                        id="exampleCheck2">
                                                    <label class="form-check-label" for="exampleCheck2">Express
                                                        Delivery</label>
                                                </div>

                                                <div class="mb-4 form-check">
                                                    <input type="hidden" name="insurance" value="0">
                                                    <input type="checkbox" class="form-check-input" name="insurance" value="1"
                                                        id="exampleCheck3">
                                                    <label class="form-check-label"
                                                        for="exampleCheck3">Insurance</label>
                                                </div>

                                                <div class="mb-4 form-check">
                                                    <input type="hidden" name="packaging" value="0">
                                                    <input type="checkbox" class="form-check-input" name="packaging" value="1"
                                                        id="exampleCheck4">
                                                    <label class="form-check-label"
                                                        for="exampleCheck4">Packaging</label>
                                                </div>
                                            </div>
                                        </div>



                                        <div class="col-lg-12 col-md-12">
                                            <div class="tw-booking-footer">
                                                <div class="tw-booking-footer-btn">
                                                    <button type="submit" class="btn-half site-button">
                                                        <span>Submit Now</span><em></em>
                                                    </button>
                                                </div>
                                            </div>

                                        </div>

                                    </div>

                                </form>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-12 rightSidebar side-bar">
                    <div class="widget search-bx">

                        <form role="search" method="post">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search">
                                <button class="btn" type="button" id="button-addon2"><i class="fa fa-search"></i></button>
                            </div>
                        </form>

                    </div>

                    <div class="widget all_services_list">
                        <h4 class="section-head-small mb-4">Transport Services</h4>
                        <div class="all_services m-b30">
                            <ul>
                                @foreach($services as $service)
                                @if(!empty($service->name))
                                <li><a href="{{route('service-single', $service->slug)}}">{{$service->name}}</a></li>
                                @endif
                                @endforeach
                            </ul>
                        </div>
                    </div>

                    <div class="widget recent-posts-entry">
                        <h4 class="section-head-small mb-4">Popular Post</h4>
                        <div class="section-content">
                            <div class="widget-post-bx">
                                @foreach($posts as $post)
                                @if(!empty($post->title))
                                <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="{{asset('storage/'.$post->featured_image)}}" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">{{ \Carbon\Carbon::parse($post->created_at)->format('F d, Y') }}</span>
                                            <span class="post-title">
                                                <a href="{{ route('blog-single', $post->slug) }}">{{ Str::limit(strip_tags($post->short_description), 60, '...') }}</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                @endforeach
                                {{-- <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="images/blog/recent-blog/pic1.jpg" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">January 08, 2022</span>
                                            <span class="post-title">
                                                <a href="services-1.html">Equipment you can count on. People you can trust.</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="images/blog/recent-blog/pic2.jpg" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">January 12, 2022</span>
                                            <span class="post-title">
                                                <a href="services-1.html">Advanced Service Functions by Air Transport</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="images/blog/recent-blog/pic3.jpg" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">January 16, 2022</span>
                                            <span class="post-title">
                                                <a href="services-1.html">Proper arrangement for keeping the goods in the warehouse</a>
                                            </span>
                                        </div>
                                    </div>
                                </div> --}}

                            </div>
                        </div>
                    </div>



                    <div class="widget tw-sidebar-gallery-wrap">
                        <h4 class="section-head-small mb-4">Gallery</h4>
                        <div class="tw-sidebar-gallery">
                            <ul>
                                @foreach($galleryImages->take(6) as $image)
                                @if(!empty($image->image))
                                <li>
                                    <div class="tw-service-gallery-thumb">
                                        <a class="elem" href="frontend/images/gallery/thumb/pic1.jpg" title="Title 1" data-lcl-author="" data-lcl-thumb="frontend/images/gallery/thumb/pic1.jpg">
                                            <img src="{{asset('storage/'.$image->image)}}" alt="">
                                            <i class="fa fa-file-image-o"></i>
                                        </a>
                                    </div>
                                </li>
                                @endif
                                @endforeach

                            </ul>

                        </div>
                    </div>



                    <div class="widget tw-contact-bg-section">
                        <h4 class="section-head-small mb-4">Any Emergency?</h4>
                        <div class="tw-contact-bg-inner" style="background-image: url({{ asset('frontend/images/background/bg-4.jpg')}});">
                           <div class="section-top">
                            @if(!empty($contact->phone))
                               <span>Call Our 24/7 Customer Support</span>
                               <h3 class="tw-con-number"><a href="tel:+9(465)3212055"> {{$contact->phone}}</a></h3>
                            @endif
                           </div>
                           <div class="section-bot">
                               <ul>
                                <li>
                                    <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    @if(!empty($contact->address))
                                    <p>{{$contact->address}} </p>
                                    @endif
                                </li>
                                <li>
                            <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    <p>{{$contact->email}}</p>
                                </li>
                               </ul>
                           </div>
                        </div>
                    </div>


                </div>

            </div>
        </div>

    </div>
    <!-- SERVICE DETAIL SECTION END -->

</div>

@endsection
