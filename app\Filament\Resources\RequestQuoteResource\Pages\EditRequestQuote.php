<?php

namespace App\Filament\Resources\RequestQuoteResource\Pages;

use App\Filament\Resources\RequestQuoteResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRequestQuote extends EditRecord
{
    protected static string $resource = RequestQuoteResource::class;

    public function getTitle(): string
    {
        return 'Edit Request Quote Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
