<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Clusters\Testimonial;
use App\Filament\Resources\TestimonialsContentResource\Pages;
use App\Filament\Resources\TestimonialsContentResource\RelationManagers;
use App\Models\TestimonialsContent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TestimonialsContentResource extends Resource
{
    protected static ?string $model = TestimonialsContent::class;

    protected static ?string $navigationIcon = 'clarity-tags-solid-alerted';
    protected static ?string $cluster = Testimonial::class;

    public static function getNavigationLabel(): string
    {
        return 'Testimonial Contents';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Name')
                    ->required(),
                Forms\Components\TextInput::make('position')
                    ->label('Position')
                    ->required(),
                Forms\Components\Textarea::make('message')
                    ->label('Message')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('position')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('message')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonialsContents::route('/'),
            'create' => Pages\CreateTestimonialsContent::route('/create'),
            'edit' => Pages\EditTestimonialsContent::route('/{record}/edit'),
        ];
    }
}
