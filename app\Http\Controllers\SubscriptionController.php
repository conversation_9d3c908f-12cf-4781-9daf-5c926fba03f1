<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    public function store(Request $request){
        $validated = $request->validate([
            'email' => 'required|email|max:255',
        ]);

        Subscription::create($validated);

        return redirect()->route('home')->with('success', 'Grate You Have joined Our Newsletter successfully!');
    }
}
