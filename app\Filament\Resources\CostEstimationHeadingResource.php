<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Clusters\ServicePageSetting;
use App\Filament\Resources\CostEstimationHeadingResource\Pages;
use App\Filament\Resources\CostEstimationHeadingResource\RelationManagers;
use App\Models\CostEstimationHeading;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CostEstimationHeadingResource extends Resource
{
    protected static ?string $model = CostEstimationHeading::class;

    protected static ?string $navigationIcon = 'heroicon-s-pencil-square';

    protected static ?string $cluster = ServicePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'Cost Estimation Heading';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('content')
                    ->label('Content')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCostEstimationHeadings::route('/'),
            'create' => Pages\CreateCostEstimationHeading::route('/create'),
            'edit' => Pages\EditCostEstimationHeading::route('/{record}/edit'),
        ];
    }
}
