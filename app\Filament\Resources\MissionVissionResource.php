<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\About;
use App\Filament\Resources\MissionVissionResource\Pages;
use App\Filament\Resources\MissionVissionResource\RelationManagers;
use App\Models\MissionVission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MissionVissionResource extends Resource
{
    protected static ?string $model = MissionVission::class;

    protected static ?string $navigationIcon = 'clarity-target-line';
    protected static ?string $cluster = About::class;

    public static function getNavigationLabel(): string
    {
        return 'Mission And Vission';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('mission_vission')->required()->label('Mission And Vission')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('mission_vission')->label('Mission And Vission')
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->slideOver('true'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMissionVissions::route('/'),
            'create' => Pages\CreateMissionVission::route('/create'),
            'edit' => Pages\EditMissionVission::route('/{record}/edit'),
        ];
    }
}
