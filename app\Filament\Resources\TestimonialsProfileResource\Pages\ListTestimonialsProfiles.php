<?php

namespace App\Filament\Resources\TestimonialsProfileResource\Pages;

use App\Filament\Resources\TestimonialsProfileResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTestimonialsProfiles extends ListRecords
{
    protected static string $resource = TestimonialsProfileResource::class;

    public function getTitle():string
    {
        return 'Testimonial Profile';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
