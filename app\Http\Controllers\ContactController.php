<?php

namespace App\Http\Controllers;

use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    public function store(Request $request)
    {
        try {
        $request->validate([
            'username' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'required|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Option 1: Save to database
        Message::create($request->all());


        return redirect()->route('contact')->with('success', 'Quote submitted successfully!');
    }catch (\Illuminate\Validation\ValidationException $e) {
        dd($e->errors());
    }
    }
}
