@extends('frontend.layouts.master')
@section('content')

<div class="page-content">

    <!-- INNER PAGE BANNER -->
    <div class="wt-bnr-inr overlay-wraper bg-center" style="background-image:url({{ asset('frontend/images/banner/1.jpg')}});">
        <div class="overlay-main site-bg-sky opacity-08"></div>
        <div class="container">
            <div class="wt-bnr-inr-entry">
                <div class="banner-title-outer">
                    <div class="banner-title-name">
                        <h2 class="wt-title">Project Detail</h2>
                    </div>
                </div>
                <!-- BREADCRUMB ROW -->

                    <div>
                        <ul class="wt-breadcrumb breadcrumb-style-2">
                            <li><a href="index.html">Home</a></li>
                            <li>Project Detail</li>
                        </ul>
                    </div>

                <!-- BREADCRUMB ROW END -->
            </div>
        </div>
    </div>
    <!-- INNER PAGE BANNER END -->



<!-- OUR BLOG START -->
<div class="section-full  p-t120 p-b90 bg-white">
    <div class="container">

        <!-- BLOG SECTION START -->
        <div class="section-content">
            <div class="row d-flex justify-content-center">

                <div class="col-lg-8 col-md-12">
                    <!-- BLOG START -->
                    <div class="blog-post-single-outer">
                        <div class="blog-post-single bg-white">

                            <div class="wt-post-info">

                                <div class="wt-post-media m-b30">
                                    <img src="{{ asset('storage/'. $project->image) }}" alt="">
                                </div>

                                <div class="wt-post-title ">
                                    <div class="wt-post-meta-list">
                                        <div class="wt-list-content post-date">{{ \Carbon\Carbon::parse($project->created_at)->format('F d, Y') }}</div>
                                        <div class="wt-list-content post-comment">{{ $project->client_name}}</div>
                                        <div class="wt-list-content post-view">{{$project->tag}}</div>
                                    </div>
                                    <h3 class="post-title">{{ $project->name }}</h3>

                                </div>

                                <div class="wt-post-discription">

                                    <p>
                                        @if(!empty($project->description))
                                        {!! $project->description !!}
                                        @endif
                                    </p>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-6 col-sm-6">
                                            <div class="one-column1 mb-3">
                                                <div class="wt-media">
                                                    @if(!empty($project->project_detail_image1))
                                                    <img src="{{ asset('storage/'. $project->project_detail_image1)}}" alt="" class="img-responsive">
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 col-md-6 col-sm-6">
                                            <div class="one-column2 mb-3">
                                                <div class="wt-media">
                                                    @if(!empty($project->project_detail_image1))
                                                    <img src="{{ asset('storage/'. $project->project_detail_image2)}}" alt="" class="img-responsive">
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                  </div>

                                </div>

                                <div class="post-single-list">
                                    @if(!empty($project->project_detail))
                                    {!! $project->project_detail !!}
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-12 rightSidebar side-bar">
                    <div class="widget search-bx">

                        <form role="search" action="{{route('search')}}" method="GET">
                            <div class="input-group">
                                <input class="form-control" value="" name="q" type="search" placeholder="Type to search"/>
                                <button class="btn" type="submit" id="button-addon2"><i class="fa fa-search"></i></button>
                            </div>
                        </form>

                    </div>

                    <div class="widget all_services_list">
                        <h4 class="section-head-small mb-4">Transport Services</h4>
                        <div class="all_services m-b30">
                            <ul>
                                @foreach($services as $service)
                                @if(!empty($service->name))
                                <li><a href="{{route('service-single', $service->slug)}}">{{$service->name}}</a></li>
                                @endif
                                @endforeach
                            </ul>
                        </div>
                    </div>

                    <div class="widget recent-posts-entry">
                        <h4 class="section-head-small mb-4">Popular Post</h4>
                        <div class="section-content">
                            <div class="widget-post-bx">
                                @foreach($posts as $post)
                                @if(!empty($post->title))
                                <div class="widget-post clearfix">
                                    <div class="wt-post-media">
                                        <img src="{{asset('storage/'.$post->featured_image)}}" alt="">
                                    </div>
                                    <div class="wt-post-info">
                                        <div class="wt-post-header">
                                            <span class="post-date">{{ \Carbon\Carbon::parse($post->created_at)->format('F d, Y') }}</span>
                                            <span class="post-title">
                                                <a href="{{ route('blog-single', $post->slug) }}">{{ Str::limit(strip_tags($post->short_description), 60, '...') }}</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="widget tw-sidebar-gallery-wrap">
                        <h4 class="section-head-small mb-4">Gallery</h4>
                        <div class="tw-sidebar-gallery">
                            <ul>
                                @foreach($galleryImages->take(6) as $image)
                                @if(!empty($image->image))
                                <li>
                                    <div class="tw-service-gallery-thumb">
                                        <a class="elem" href="frontend/images/gallery/thumb/pic1.jpg" title="Title 1" data-lcl-author="" data-lcl-thumb="frontend/images/gallery/thumb/pic1.jpg">
                                            <img src="{{asset('storage/'.$image->image)}}" alt="">
                                            <i class="fa fa-file-image-o"></i>
                                        </a>
                                    </div>
                                </li>
                                @endif
                                @endforeach


                            </ul>

                        </div>
                    </div>
                    <div class="widget tw-contact-bg-section">
                        <h4 class="section-head-small mb-4">Any Emergency?</h4>
                        <div class="tw-contact-bg-inner" style="background-image: url({{ asset('frontend/images/background/bg-4.jpg')}});">
                           <div class="section-top">
                               <span>Call Our 24/7 Customer Support</span>
                               @if(!empty($contact->phone))
                               <h3 class="tw-con-number"><a href="tel:+9(465)3212055">{{$contact->phone}}</a></h3>
                               @endif
                           </div>
                           <div class="section-bot">
                               <ul>
                                <li>
                                    <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    @if(!empty($contact->address))
                                    <p>{{$contact->address}}</p>
                                    @endif
                                </li>
                                <li>
                                    <span><img src="{{ asset('frontend/images/icons/map-marker.png')}}" alt=""></span>
                                    @if(!empty($contact->email))
                                    <p>{{$contact->email}}</p>
                                    @endif
                                </li>
                               </ul>
                           </div>
                        </div>
                    </div>


                </div>

            </div>

        </div>

    </div>

</div>
<!-- OUR BLOG END -->


</div>

@endsection
