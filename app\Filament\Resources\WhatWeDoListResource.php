<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\WhatWeDoListResource\Pages;
use App\Filament\Resources\WhatWeDoListResource\RelationManagers;
use App\Models\WhatWeDoList;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WhatWeDoListResource extends Resource
{
    protected static ?string $model = WhatWeDoList::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $cluster = HomePageSetting::class;

    public static function getNavigationLabel(): string
    {
    return 'What We Do List';
    }

    public function getTitle(): string
    {
    return 'What We Do List';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('content')
                    ->label('Content')
                    ->required(),
                Forms\Components\FileUpload::make('featured_image')
                    ->label('Featured Image')
                    ->image(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('Featured Image')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhatWeDoLists::route('/'),
            'create' => Pages\CreateWhatWeDoList::route('/create'),
            'edit' => Pages\EditWhatWeDoList::route('/{record}/edit'),
        ];
    }
}
