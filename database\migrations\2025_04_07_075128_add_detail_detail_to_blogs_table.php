<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blogs', function (Blueprint $table) {
            $table->text('detail_description')->after('description')->nullable();
            $table->string('detail_image1')->after('detail_description')->nullable();
            $table->string('detail_image2')->after('detail_image1')->nullable();
            $table->text('quote')->after('detail_image2')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blogs', function (Blueprint $table) {
            $table->dropColumn('detail_description');
            $table->dropColumn('detail_image1');
            $table->dropColumn('detail_image2');
            $table->dropColumn('quote');
        });
    }
};
