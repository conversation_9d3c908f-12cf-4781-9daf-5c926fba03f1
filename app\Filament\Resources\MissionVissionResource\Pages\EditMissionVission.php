<?php

namespace App\Filament\Resources\MissionVissionResource\Pages;

use App\Filament\Resources\MissionVissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMissionVission extends EditRecord
{
    protected static string $resource = MissionVissionResource::class;

    public function getTitle():string
    {
        return 'Edit Mission And Vission';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
