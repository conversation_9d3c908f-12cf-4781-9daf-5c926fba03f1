<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListServiceSolutions extends ListRecords
{
    protected static string $resource = ServiceSolutionResource::class;

    public function getTitle(): string
    {
        return 'Solutions';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
