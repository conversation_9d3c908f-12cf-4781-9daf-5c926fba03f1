<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources;

use App\Filament\Clusters\ServicePageSetting;
use App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource\Pages;
use App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource\RelationManagers;
use App\Models\ServiceHeading;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceHeadingResource extends Resource
{
    protected static ?string $model = ServiceHeading::class;

    protected static ?string $navigationIcon = 'clarity-file-settings-line';

    public static function getNavigationLabel(): string
    {
        return 'Service Heading';
    }

    protected static ?string $cluster = ServicePageSetting::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('content')
                    ->label('Content')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServiceHeadings::route('/'),
            'create' => Pages\CreateServiceHeading::route('/create'),
            'edit' => Pages\EditServiceHeading::route('/{record}/edit'),
        ];
    }
}
