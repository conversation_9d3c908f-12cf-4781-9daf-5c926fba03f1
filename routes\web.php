<?php

use App\Http\Controllers\ContactController;
use App\Http\Controllers\FrontendController;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    $whatwedo = app(FrontendController::class)->getWhatWeDoHeading();
    $whatwedolist = app(FrontendController::class)->getWhatWeDoList();
    $whychooseus = app(FrontendController::class)->getWhyChooseUs();
    $requestquoteheading =app(FrontendController::class)->getRequestQuoteHeading();
    $aboutcompany = app(FrontendController::class)->getAboutCompany();
    $estimation = app(FrontendController::class)->getEstimationHeading();
    $solutions = app(FrontendController::class)->getSolution();
    $testimonialprofile = app(FrontendController::class)->getTestimonialProfile();
    $testimonialcontent = app(FrontendController::class)->getTestimonialContent();
    $partners = app(FrontendController::class)->getPartner();
    $serviceintro = app(FrontendController::class)->getServiceIntro();
    $services = app(FrontendController::class)->getService();
    $blogintro = app(FrontendController::class)->getBlogIntro();
    $posts = app(FrontendController::class)->getBlog();
    $projects = app(FrontendController::class)->getProject();
    $approaches = app(FrontendController::class)->getApproach();
    $slides = app(FrontendController::class)->getSlider();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('welcome', compact('whatwedo', 'whatwedolist', 'whychooseus', 'requestquoteheading', 'aboutcompany',
    'estimation', 'solutions', 'testimonialprofile', 'testimonialcontent', 'partners', 'serviceintro', 'services',
    'blogintro', 'posts', 'projects', 'approaches', 'slides', 'contact','siteId', 'subscriptionHeading'));
})->name('home');

Route::get('/about-us', function () {
    $mission = app(FrontendController::class)->getMission();
    $overview = app(FrontendController::class)->getOverView();
    $aboutcompany = app(FrontendController::class)->getAboutCompany();
    $services = app(FrontendController::class)->getService();
    $whatwedo = app(FrontendController::class)->getWhatWeDoHeading();
    $aboutcompany = app(FrontendController::class)->getAboutCompany();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('about-us', compact('aboutcompany','siteId', 'contact', 'subscriptionHeading', 'whatwedo', 'services', 'aboutcompany', 'overview', 'mission'));
})->name('about');

Route::get('/overview', function () {
    $overview = app(FrontendController::class)->getOverView();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('overview', compact('overview','siteId', 'contact', 'subscriptionHeading'));
})->name('overview');

Route::get('/contact-us', function () {
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('contact-us', compact('siteId', 'contact', 'subscriptionHeading'));
})->name('contact');

Route::get('/coming-soon', function () {
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('commingsoon', compact('siteId', 'contact', 'subscriptionHeading'));
});

Route::get('/services', function () {
    $services = app(FrontendController::class)->getService();
    $serviceHeading = app(FrontendController::class)->getServiceHeading();
    $costHeading = app(FrontendController::class)->getCostEstimationHeading();
    $serviceSolutions = app(FrontendController::class)->getServiceSolution();
    $quoteHeading = app(FrontendController::class)->getQuoteHeading();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('service', compact('contact','serviceHeading', 'services', 'siteId', 'costHeading', 'serviceSolutions', 'quoteHeading', 'subscriptionHeading'));
})->name('services');

Route::get('/gallery', function () {
    $galleryHeading = app(FrontendController::class)->getGalleryHeading();
    $galleryImages = app(FrontendController::class)->getGalleryImage();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('gallery', compact('galleryHeading','galleryImages', 'siteId', 'contact', 'subscriptionHeading'));
});

Route::get('/mission', function () {
    $mission = app(FrontendController::class)->getMission();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('mission', compact('mission','siteId', 'contact', 'subscriptionHeading'));
})->name('mission');

Route::get('/news', function () {
    $posts = app(FrontendController::class)->getBlog();
    $news = app(FrontendController::class)->getNews();
    $galleryImages = app(FrontendController::class)->getGalleryImage();
    $siteId = app(FrontendController::class)->getSiteId();
    $contact = app(FrontendController::class)->getContact();
    $subscriptionHeading = app(FrontendController::class)->getSubscriptionHeading();
    return view('news', compact('news', 'posts', 'galleryImages','siteId', 'contact', 'subscriptionHeading'));
})->name('news');

Route::get('/blog/{slug}', [FrontendController::class, 'blogDetail'])->name('blog-single');
Route::get('/news/{slug}', [FrontendController::class, 'NewsDetail'])->name('news-single');
Route::get('/service/{slug}', [FrontendController::class, 'serviceDetail'])->name('service-single');
Route::get('/project/{slug}', [FrontendController::class, 'projectDetail'])->name('single-project');
Route::get('/track-shipment', [ShipmentController::class, 'track'])->name('track.shipment');
Route::resource('quotes', QuoteController::class);
Route::post('/store', [SubscriptionController::class, 'store'])->name('subscription');
Route::post('/contact-form', [ContactController::class, 'store'])->name('contact.store');
Route::get('/search', [SearchController::class, 'search'])->name('search');

