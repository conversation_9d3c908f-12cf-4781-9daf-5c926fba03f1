<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Clusters\Testimonial;
use App\Filament\Resources\TestimonialsProfileResource\Pages;
use App\Filament\Resources\TestimonialsProfileResource\RelationManagers;
use App\Models\TestimonialsProfile;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TestimonialsProfileResource extends Resource
{
    protected static ?string $model = TestimonialsProfile::class;

    protected static ?string $navigationIcon = 'clarity-tags-solid-alerted';
    protected static ?string $cluster = Testimonial::class;

    public static function getNavigationLabel(): string
    {
        return 'Testimonial Profiles';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\FileUpload::make('profile')
                    ->label('Profile')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('profile')
                    ->label('Profile')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonialsProfiles::route('/'),
            'create' => Pages\CreateTestimonialsProfile::route('/create'),
            'edit' => Pages\EditTestimonialsProfile::route('/{record}/edit'),
        ];
    }
}
