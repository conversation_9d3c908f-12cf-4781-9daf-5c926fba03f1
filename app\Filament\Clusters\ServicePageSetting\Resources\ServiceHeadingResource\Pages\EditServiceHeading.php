<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditServiceHeading extends EditRecord
{
    protected static string $resource = ServiceHeadingResource::class;

    public function getTitle(): string
    {
        return 'Edit Service Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
