<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\AboutCompanyResource\Pages;
use App\Filament\Resources\AboutCompanyResource\RelationManagers;
use App\Models\AboutCompany;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AboutCompanyResource extends Resource
{
    protected static ?string $model = AboutCompany::class;

    protected static ?string $navigationIcon = 'clarity-license-solid';

    protected static ?string $cluster = HomePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'About Company';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\TextInput::make('counter')
                    ->label('Counter')->type('number')->numeric()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('counter')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAboutCompanies::route('/'),
            'create' => Pages\CreateAboutCompany::route('/create'),
            'edit' => Pages\EditAboutCompany::route('/{record}/edit'),
        ];
    }
}
