<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;

class ListServiceHeadings extends ListRecords
{
    protected static string $resource = ServiceHeadingResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Service Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
