<?php

namespace App\Filament\Clusters\HomePageSetting\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Clusters\HomePageSetting\Resources\SliderResource\Pages;
use App\Filament\Clusters\HomePageSetting\Resources\SliderResource\RelationManagers;
use App\Models\Slider;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SliderResource extends Resource
{
    protected static ?string $model = Slider::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = HomePageSetting::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')->label('Title')->required(),
                Forms\Components\RichEditor::make('content')->label('Content')->required(),
                Forms\Components\FileUpload::make('featured_image')->label('Featured Image')->required(),
                Forms\Components\FileUpload::make('featured_image_bg')->label('Featured Image Background')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')->searchable()->label('Title')->sortable(),
                Tables\Columns\TextColumn::make('content')->searchable()->label('Content')->sortable(),
                Tables\Columns\TextColumn::make('featured_image')->label('Featured Image'),
                Tables\Columns\TextColumn::make('featured_image_bg')->label('Featured Image Background'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSliders::route('/'),
            'create' => Pages\CreateSlider::route('/create'),
            'edit' => Pages\EditSlider::route('/{record}/edit'),
        ];
    }
}
