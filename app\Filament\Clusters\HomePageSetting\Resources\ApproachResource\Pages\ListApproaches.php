<?php

namespace App\Filament\Clusters\HomePageSetting\Resources\ApproachResource\Pages;

use App\Filament\Clusters\HomePageSetting\Resources\ApproachResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListApproaches extends ListRecords
{
    protected static string $resource = ApproachResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
