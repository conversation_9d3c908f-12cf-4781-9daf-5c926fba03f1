<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources;

use App\Filament\Clusters\ServicePageSetting;
use App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource\Pages;
use App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource\RelationManagers;
use App\Models\ServiceSolution;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceSolutionResource extends Resource
{
    protected static ?string $model = ServiceSolution::class;

    protected static ?string $navigationIcon = 'clarity-file-settings-line';

    protected static ?string $cluster = ServicePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'Solution';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\Textarea::make('content')
                    ->label('Content'),
                Forms\Components\RichEditor::make('description')
                    ->label('Description'),
                Forms\Components\FileUpload::make('image')
                    ->label('Image')


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('image')
                    ->label('Image')
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->slideOver(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServiceSolutions::route('/'),
            'create' => Pages\CreateServiceSolution::route('/create'),
            'edit' => Pages\EditServiceSolution::route('/{record}/edit'),
        ];
    }
}
