<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources;

use App\Filament\Clusters\ServicePageSetting;
use App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource\Pages;
use App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource\RelationManagers;
use App\Models\QuoteHeading;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuoteHeadingResource extends Resource
{
    protected static ?string $model = QuoteHeading::class;

    protected static ?string $navigationIcon = 'clarity-file-settings-line';

    protected static ?string $cluster = ServicePageSetting::class;

    public static function getNavigationLabel(): string
    {
        return 'Request Quote Heading';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')->required(),
                Forms\Components\TextInput::make('content')
                    ->label('Content')->required(),
                Forms\Components\FileUpload::make('featured_image')->required()->label('Featured Image'),
                Forms\Components\FileUpload::make('bg_image')->required()->label('Background Image'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()->label('Title'),
                Tables\Columns\TextColumn::make('Content')
                    ->searchable()
                    ->sortable()->label('Content'),
                Tables\Columns\ImageColumn::make('featured_image')->label('Featured Image')->sortable(),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuoteHeadings::route('/'),
            'create' => Pages\CreateQuoteHeading::route('/create'),
            'edit' => Pages\EditQuoteHeading::route('/{record}/edit'),
        ];
    }
}
