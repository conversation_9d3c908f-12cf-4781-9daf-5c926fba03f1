<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateQuoteHeading extends CreateRecord
{
    public function getTitle(): string|Htmlable
    {
        return 'Create Quote Heading';
    }
    protected static string $resource = QuoteHeadingResource::class;
}
