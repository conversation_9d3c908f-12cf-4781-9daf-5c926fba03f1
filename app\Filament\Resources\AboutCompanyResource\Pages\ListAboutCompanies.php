<?php

namespace App\Filament\Resources\AboutCompanyResource\Pages;

use App\Filament\Resources\AboutCompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAboutCompanies extends ListRecords
{
    protected static string $resource = AboutCompanyResource::class;

    public function getTitle(): string
    {
        return 'About Company';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
