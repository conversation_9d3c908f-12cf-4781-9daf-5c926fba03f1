@extends('frontend.layouts.master')
@section('content')
<style>
.shipment-card {
    max-width: 800px;
    margin: 100px auto;
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.shipment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.shipment-card h1 {
    font-size: 24px;
    color: #333;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    text-align: left;
    background: #1A182E;
    color: #fff;
    padding: 10px;
    border-radius: 6px 0 0 6px;
}

.table td {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 0 6px 6px 0;
    font-weight: 500;
}

.table tr {
    border-bottom: 1px solid #ddd;
}

.table tr:last-child {
    border-bottom: none;
}

@media (max-width: 768px) {
    .shipment-card {
        width: 90%;
    }

    .table th, .table td {
        display: block;
        width: 100%;
        text-align: left;
        border-radius: 6px;
    }

    .table tr {
        display: block;
        margin-bottom: 10px;
    }
}
</style>

<div class="shipment-card">
    <h1>Your Cargo Detail</h1>
    <div class="table-responsive">
        <table class="table">
            <tbody>
                <tr>
                    <th>ID</th>
                    <td>{{ $shipment->id }}</td>
                </tr>
                <tr>
                    <th> Plate Number </th>
                    <td> {{ $shipment->chassis_number }} </td>
                </tr>
                <tr>
                    <th> Shipment Type  </th>
                    <td> {{ $shipment->shipment_type }} </td>
                </tr>
                <tr>
                    <th> Description </th>
                    <td> {!! $shipment->description !!} </td>
                </tr>
                <tr>
                    <th> Origin </th>
                    <td> {{ $shipment->origin }} </td>
                </tr>
                <tr>
                    <th> Destination </th>
                    <td> {{ $shipment->destination }} </td>
                </tr>
                <tr>
                    <th> Weight </th>
                    <td> {{ $shipment->weight }} </td>
                </tr>
                <tr>
                    <th> Dimension </th>
                    <td> {{ $shipment->dimension }} </td>
                </tr>
                <tr>
                    <th> Quantity </th>
                    <td> {{ $shipment->quantity }} </td>
                </tr>
                <tr>
                    <th> Price </th>
                    <td> {{ $shipment->price }} </td>
                </tr>
                <tr>
                    <th> Currency </th>
                    <td> {{ $shipment->currency }} </td>
                </tr>
                <tr>
                    <th> Pickup Date </th>
                    <td> {{ $shipment->pickup_date }} </td>
                </tr>
                <tr>
                    <th> Delivery Date </th>
                    <td> {{ $shipment->delivery_date }} </td>
                </tr>
                <tr>
                    <th> Pickup Time </th>
                    <td> {{ $shipment->pickup_time }} </td>
                </tr>
                <tr>
                    <th> Delivery Time </th>
                    <td> {{ $shipment->delivery_time }} </td>
                </tr>
                <tr>
                    <th> Pickup Address </th>
                    <td> {{ $shipment->pickup_address }} </td>
                </tr>
                <tr>
                    <th> Delivery Address </th>
                    <td> {{ $shipment->delivery_address }} </td>
                </tr>
                <tr>
                    <th> Pickup Contact </th>
                    <td> {{ $shipment->pickup_contact }} </td>
                </tr>
                <tr>
                    <th> Delivery Contact </th>
                    <td> {{ $shipment->delivery_contact }} </td>
                </tr>
                <tr>
                    <th> Pickup Phone </th>
                    <td> {{ $shipment->pickup_phone }} </td>
                </tr>
                <tr>
                    <th> Delivery Phone </th>
                    <td> {{ $shipment->delivery_phone }} </td>
                </tr>
                <tr>
                    <th> Pickup Email </th>
                    <td> {{ $shipment->pickup_email }} </td>
                </tr>
                <tr>
                    <th> Delivery Email </th>
                    <td> {{ $shipment->delivery_email }} </td>
                </tr>
                <tr>
                    <th> Pickup Instruction </th>
                    <td> {{ $shipment->pickup_instruction }} </td>
                </tr>
                <tr>
                    <th> Delivery Instruction </th>
                    <td> {{ $shipment->delivery_instruction }} </td>
                </tr>
                <tr>
                    <th> Fragile </th>
                    <td> {{ $shipment->fragile? 'Yes' : 'No' }} </td>
                </tr>
                <tr>
                    <th> Express Delivery </th>
                    <td> {{ $shipment->express_delivery? 'Yes' : 'No' }} </td>
                </tr>
                <tr>
                    <th> Insurance </th>
                    <td> {{ $shipment->insurance? 'Yes' : 'No' }} </td>
                </tr>
                <tr>
                    <th> Packaging </th>
                    <td> {{ $shipment->packaging? 'Yes' : 'No' }} </td>
                </tr>
                <tr>
                    <th> Status </th>
                    <td> {{ $shipment->status }} </td>
                </tr>
                <tr>
                    <th> Created At </th>
                    <td> {{ $shipment->created_at }} </td>
                </tr>
                <tr>
                    <th> Updated At </th>
                    <td> {{ $shipment->updated_at }} </td>
                </tr>

            </tbody>
        </table>
    </div>
</div>


@endsection
