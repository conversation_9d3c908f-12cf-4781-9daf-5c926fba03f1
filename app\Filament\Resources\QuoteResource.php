<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\QuoteResource\Pages;
use App\Filament\Resources\QuoteResource\RelationManagers;
use App\Models\Quote;
use Filament\Forms;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\NumberColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuoteResource extends Resource
{
    protected static ?string $model = Quote::class;

    protected static ?string $navigationIcon = 'heroicon-c-chat-bubble-left-ellipsis';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('username')->label('Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('company_name')->label('Company Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')->label('Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')->label('Phone')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('freight_type')->label('Service Type')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city_of_departure')->label('Origin')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('delivery_city')->label('Destination')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('incoterms')->label('Commodity Type')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('weight')->label('Weight')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('height')->label('Height')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('width')->label('Width')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('length')->label('Length')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('message')->label('Description')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('fragile')->label('Fragile')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('express_delivery')->label('Express Delivery')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('insurance')->label('Insurance')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('packaging')->label('Packaging')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuotes::route('/'),
            'create' => Pages\CreateQuote::route('/create'),
            'edit' => Pages\EditQuote::route('/{record}/edit'),
        ];
    }
}
