<?php

namespace App\Models;

use App\Events\ShipmentStatusChanged;
use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    protected $fillable = [
        'chassis_number',
        'shipment_type',
        'description',
        'origin',
        'destination',
        'weight',
        'dimension',
        'quantity',
        'price',
        'currency',
        'pickup_date',
        'delivery_date',
        'pickup_time',
        'delivery_time',
        'pickup_address',
        'delivery_address',
        'pickup_contact',
        'delivery_contact',
        'pickup_email',
        'delivery_email',
        'pickup_phone',
        'delivery_phone',
        'pickup_instruction',
        'delivery_instruction',
        'fragile',
        'express_delivery',
        'insurance',
        'packaging',
        'status',
    ];

    protected static function boot()
    {
        parent::boot();

        static::updating(function ($shipment) {
            if ($shipment->isDirty('status')) {
                // Trigger the event when the status changes
                event(new ShipmentStatusChanged($shipment));
            }
        });
    }
}
