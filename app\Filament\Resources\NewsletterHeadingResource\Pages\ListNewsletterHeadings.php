<?php

namespace App\Filament\Resources\NewsletterHeadingResource\Pages;

use App\Filament\Resources\NewsletterHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNewsletterHeadings extends ListRecords
{
    protected static string $resource = NewsletterHeadingResource::class;

    public function getTitle():string
    {
        return 'Subscription Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
