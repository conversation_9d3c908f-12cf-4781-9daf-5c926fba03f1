<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->string('client_name')->after('description')->nullable();
            $table->text('project_detail')->after('client_name')->nullable();
            $table->string('project_detail_image1')->after('project_detail')->nullable();
            $table->string('project_detail_image2')->after('project_detail_image1')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('client_name');
            $table->dropColumn('project_detail');
            $table->dropColumn('project_detail_image1');
            $table->dropColumn('project_detail_image2');

        });
    }
};
