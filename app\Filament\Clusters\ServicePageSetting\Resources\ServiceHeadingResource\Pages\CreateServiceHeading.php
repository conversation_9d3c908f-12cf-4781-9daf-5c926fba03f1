<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateServiceHeading extends CreateRecord
{
    public function getTitle(): string|Htmlable
    {
        return 'Create Service Heading';
    }
    protected static string $resource = ServiceHeadingResource::class;
}
