<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\Subscription;
use App\Filament\Resources\NewsletterHeadingResource\Pages;
use App\Filament\Resources\NewsletterHeadingResource\RelationManagers;
use App\Models\NewsletterHeading;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class NewsletterHeadingResource extends Resource
{
    protected static ?string $model = NewsletterHeading::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Subscription Headings';

    protected static ?string $cluster = Subscription::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('heading')->label('Heading')
                    ->required(),
                Forms\Components\FileUpload::make('image')->label('Image')
                    ->required()

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('heading')->label('Heading'),
                Tables\Columns\ImageColumn::make('image')->label('Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNewsletterHeadings::route('/'),
            'create' => Pages\CreateNewsletterHeading::route('/create'),
            'edit' => Pages\EditNewsletterHeading::route('/{record}/edit'),
        ];
    }
}
