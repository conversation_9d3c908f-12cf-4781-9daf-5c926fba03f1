<?php

namespace App\Models;

use App\Observers\QuoteObserver;
use Filament\Panel\Concerns\HasNotifications;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
#[ObservedBy([QuoteObserver::class])]
class Quote extends Model
{

    protected $fillable = [
        'username', 'company_name','email', 'phone', 'freight_type', 'city_of_departure',
        'delivery_city', 'incoterms', 'weight', 'height', 'width', 'length', 'message',
        'fragile', 'express_delivery', 'insurance', 'packaging'
    ];

    protected $casts = [
        'fragile' => 'boolean',
        'express_delivery' => 'boolean',
        'insurance' => 'boolean',
        'packaging' => 'boolean',
    ];


}
