<?php

namespace App\Filament\Resources\EstimationIntroResource\Pages;

use App\Filament\Resources\EstimationIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEstimationIntros extends ListRecords
{
    protected static string $resource = EstimationIntroResource::class;

    public function getTitle():string
    {
        return 'Estimation Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
