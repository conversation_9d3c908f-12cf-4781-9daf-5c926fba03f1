<?php

namespace App\Filament\Clusters\Gallery\Resources;

use App\Filament\Clusters\Gallery;
use App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource\Pages;
use App\Filament\Clusters\Gallery\Resources\GalleryHeadingResource\RelationManagers;
use App\Models\GalleryHeading;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Textarea;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GalleryHeadingResource extends Resource
{
    protected static ?string $model = GalleryHeading::class;

    protected static ?string $navigationIcon = 'clarity-image-gallery-solid';

    public static function getNavigationLabel(): string
    {
        return 'Gallery Heading';
    }
    protected static ?string $cluster = Gallery::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')->required()->label('Title'),
                Forms\Components\Textarea::make('content')->required()->label('Content'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->sortable()
                    ->searchable()
                    ->label('Title'),
                Tables\Columns\TextColumn::make('content')
                    ->sortable()
                    ->searchable()
                    ->label('Content'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGalleryHeadings::route('/'),
            'create' => Pages\CreateGalleryHeading::route('/create'),
            'edit' => Pages\EditGalleryHeading::route('/{record}/edit'),
        ];
    }
}
