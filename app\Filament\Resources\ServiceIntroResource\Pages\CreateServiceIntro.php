<?php

namespace App\Filament\Resources\ServiceIntroResource\Pages;

use App\Filament\Resources\ServiceIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateServiceIntro extends CreateRecord
{
    public function getTitle(): string
    {
        return 'Create Service Heading';
    }
    protected static string $resource = ServiceIntroResource::class;
}
