<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipmentResource\Pages;
use App\Filament\Resources\ShipmentResource\RelationManagers;
use App\Models\Shipment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShipmentResource extends Resource
{
    protected static ?string $model = Shipment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('chassis_number')
                    ->label('Plate Number')->unique(ignoreRecord: true)
                    ->required(),
                Forms\Components\Select::make('shipment_type')
                    ->label('Shipment Type')
                    ->options([
                        'Container' => 'Container',
                        'Truck' => 'Truck',
                        'Air' => 'Air',
                        'Sea' => 'Sea',
                        'Rail' => 'Rail',
                    ])
                    ->required(),
                Forms\Components\RichEditor::make('description')
                    ->label('Description')
                    ->required(),
                Forms\Components\TextInput::make('origin')
                    ->label('Origin')
                    ->required(),
                Forms\Components\TextInput::make('destination')
                    ->label('Destination')
                    ->required(),
                Forms\Components\TextInput::make('weight')
                    ->label('Weight')->type('number')
                    ->required(),
                Forms\Components\TextInput::make('dimension')
                    ->label('Dimension')
                    ->required(),
                Forms\Components\TextInput::make('quantity')
                    ->label('Quantity')->type('number')
                    ->required(),
                Forms\Components\TextInput::make('price')
                    ->label('Price')->type('number')
                    ->required(),
                Forms\Components\TextInput::make('currency')
                    ->label('Currency')
                    ->required(),
                Forms\Components\DatePicker::make('pickup_date')
                    ->label('Pickup Date')
                    ->required(),
                Forms\Components\DatePicker::make('delivery_date')
                    ->label('Delivery Date')
                    ->required(),
                Forms\Components\TimePicker::make('pickup_time')
                    ->label('Pickup Time')
                    ->required(),
                Forms\Components\TimePicker::make('delivery_time')
                    ->label('Delivery Time')
                    ->required(),
                Forms\Components\Textarea::make('pickup_address')
                    ->label('Pickup Address')
                    ->required(),
                Forms\Components\Textarea::make('delivery_address')
                    ->label('Delivery Address')
                    ->required(),
                Forms\Components\TextInput::make('pickup_contact')
                    ->label('Pickup Contact')
                    ->required(),
                Forms\Components\TextInput::make('delivery_contact')
                    ->label('Delivery Contact')
                    ->required(),
                Forms\Components\TextInput::make('pickup_email')
                    ->label('Pickup Email')
                    ->required(),
                Forms\Components\TextInput::make('delivery_email')
                    ->label('Delivery Email')
                    ->required(),
                Forms\Components\TextInput::make('pickup_phone')
                    ->label('Pickup Phone')
                    ->required(),
                Forms\Components\TextInput::make('delivery_phone')
                    ->label('Delivery Phone')
                    ->required(),
                Forms\Components\Textarea::make('pickup_instruction')
                    ->label('Pickup Instruction')
                    ->required(),
                Forms\Components\Textarea::make('delivery_instruction')
                    ->label('Delivery Instruction')
                    ->required(),
                Forms\Components\Checkbox::make('fragile')
                    ->label('Fragile'),
                Forms\Components\Checkbox::make('express_delivery')
                    ->label('Express Delivery'),
                Forms\Components\Checkbox::make('insurance')
                    ->label('Insurance'),
                Forms\Components\Checkbox::make('packaging')
                    ->label('Packaging'),
                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'Pending' => 'Pending',
                        'In Transit' => 'In Transit',
                        'Delivered' => 'Delivered',
                        'Cancelled' => 'Cancelled',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('chassis_number')
                    ->label('Plate Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('shipment_type')
                    ->label('Shipment Type')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('origin')
                    ->label('Origin')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('destination')
                    ->label('Destination')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('weight')
                    ->label('Weight')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label('Price')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\SelectColumn::make('status')
                    ->options([
                        'Pending' => 'Pending',
                        'In Transit' => 'In Transit',
                        'Delivered' => 'Delivered',
                        'Cancelled' => 'Cancelled',])
                    ->label('Status')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->searchable()
                    ->sortable(),


            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipments::route('/'),
            'create' => Pages\CreateShipment::route('/create'),
            'edit' => Pages\EditShipment::route('/{record}/edit'),
        ];
    }
}
