<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\QuoteHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;

class EditQuoteHeading extends EditRecord
{
    protected static string $resource = QuoteHeadingResource::class;

    public function getTitle(): string|Htmlable
    {
        return 'Edit Quote Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
