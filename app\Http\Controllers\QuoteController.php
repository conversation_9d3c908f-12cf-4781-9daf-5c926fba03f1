<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;

class QuoteController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request->all());
        try {
        $validated=$request->validate([
            'company_name' => 'nullable|string|max:255',
            'username' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'freight_type' => 'required|string',
            'city_of_departure' => 'required|string|max:255',
            'delivery_city' => 'required|string|max:255',
            'incoterms' => 'required|string',
            'weight' => 'required|numeric|min:0',
            'height' => 'required|numeric|min:0',
            'width' => 'required|numeric|min:0',
            'length' => 'required|numeric|min:0',
            'message' => 'nullable|string|max:1000',
            'fragile' => 'nullable|in:1,0,"1","0"',
            'express_delivery' => 'nullable|in:1,0,"1","0"',
            'insurance' => 'nullable|in:1,0,"1","0"',
            'packaging' => 'nullable|in:1,0,"1","0"',
        ]);

        Quote::create($validated);

        return redirect()->route('home')->with('success', 'Quote submitted successfully!');
    }catch (\Illuminate\Validation\ValidationException $e) {
        dd($e->errors());
    } }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
