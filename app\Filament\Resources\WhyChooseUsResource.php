<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Resources\WhyChooseUsResource\Pages;
use App\Filament\Resources\WhyChooseUsResource\RelationManagers;
use App\Models\WhyChooseUs;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WhyChooseUsResource extends Resource
{
    protected static ?string $model = WhyChooseUs::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $cluster = HomePageSetting::class;

    public static function getNavigationLabel():string
    {
        return 'Why Choose Us';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required(),
                Forms\Components\RichEditor::make('content')
                    ->label('Content')
                    ->required(),
                Forms\Components\FileUpload::make('bg_image')
                    ->label('Background Image')
                    ->required(),
                Forms\Components\FileUpload::make('featured_img1')
                    ->label('Featured Image One')
                    ->required(),
                Forms\Components\FileUpload::make('featured_img2')
                    ->label('Featured Image Two')
                    ->required(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')->label('Title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('content')->label("Content")
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('bg_image')->label('Background Image')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('featured_img1')->label('Background Image One')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('featured_img2')->label('Background Image Two')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhyChooseUs::route('/'),
            'create' => Pages\CreateWhyChooseUs::route('/create'),
            'edit' => Pages\EditWhyChooseUs::route('/{record}/edit'),
        ];
    }
}
