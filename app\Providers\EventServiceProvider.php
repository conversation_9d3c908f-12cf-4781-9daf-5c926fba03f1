<?php

namespace App\Providers;

use App\Events\ShipmentStatusChanged;
use App\Listeners\SendShipmentStatusNotification;
use Illuminate\Support\ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the event and listener
        $this->app['events']->listen(
            ShipmentStatusChanged::class,
            SendShipmentStatusNotification::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
