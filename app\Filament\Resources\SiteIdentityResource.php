<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteIdentityResource\Pages;
use App\Filament\Resources\SiteIdentityResource\RelationManagers;
use App\Models\SiteIdentity;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SiteIdentityResource extends Resource
{
    protected static ?string $model = SiteIdentity::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Textarea::make("description")->label('Descriprion')->required(),
                Forms\Components\FileUpload::make('logo')->required()->label('Logo'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('description')->label('Description'),
                Tables\Columns\ImageColumn::make('logo')->label('Logo'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteIdentities::route('/'),
            'create' => Pages\CreateSiteIdentity::route('/create'),
            'edit' => Pages\EditSiteIdentity::route('/{record}/edit'),
        ];
    }
}
