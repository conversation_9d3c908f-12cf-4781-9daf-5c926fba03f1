<?php

namespace App\Filament\Resources\CostEstimationHeadingResource\Pages;

use App\Filament\Resources\CostEstimationHeadingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCostEstimationHeading extends EditRecord
{
    protected static string $resource = CostEstimationHeadingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
