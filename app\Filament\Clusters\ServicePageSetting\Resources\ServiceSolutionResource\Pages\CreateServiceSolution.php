<?php

namespace App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource\Pages;

use App\Filament\Clusters\ServicePageSetting\Resources\ServiceSolutionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateServiceSolution extends CreateRecord
{

    public function getTitle(): string|Htmlable
    {
        return 'Create Solution';
    }

    protected static string $resource = ServiceSolutionResource::class;
}
