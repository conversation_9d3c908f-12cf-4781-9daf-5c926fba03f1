<?php

namespace App\Filament\Clusters\HomePageSetting\Resources;

use App\Filament\Clusters\HomePageSetting;
use App\Filament\Clusters\HomePageSetting\Resources\ApproachResource\Pages;
use App\Filament\Clusters\HomePageSetting\Resources\ApproachResource\RelationManagers;
use App\Models\Approach;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ApproachResource extends Resource
{
    protected static ?string $model = Approach::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = HomePageSetting::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')->label('Title')->required(),
                Forms\Components\TextInput::make('counter')->label('Counter')->required()->type('number'),
                Forms\Components\FileUpload::make('icon')->label('Icon')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')->label('Title')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('counter')->label('Counter')->searchable()->sortable(),
                Tables\Columns\ImageColumn::make('icon')->label('Image'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApproaches::route('/'),
            'create' => Pages\CreateApproach::route('/create'),
            'edit' => Pages\EditApproach::route('/{record}/edit'),
        ];
    }
}
