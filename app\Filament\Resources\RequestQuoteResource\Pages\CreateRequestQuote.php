<?php

namespace App\Filament\Resources\RequestQuoteResource\Pages;

use App\Filament\Resources\RequestQuoteResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;

class CreateRequestQuote extends CreateRecord
{
    public function getTitle(): string
    {
        return 'Create Request Quote Heading';
    }
    protected static string $resource = RequestQuoteResource::class;
}
