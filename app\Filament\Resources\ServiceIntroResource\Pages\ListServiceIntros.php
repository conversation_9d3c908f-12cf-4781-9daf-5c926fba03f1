<?php

namespace App\Filament\Resources\ServiceIntroResource\Pages;

use App\Filament\Resources\ServiceIntroResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListServiceIntros extends ListRecords
{
    protected static string $resource = ServiceIntroResource::class;

    public function getTitle(): string
    {
        return 'Service Heading';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
